/**
 * 团队成员与邀请管理组件
 *
 * 功能特性：
 * - 统一页面显示团队成员列表和邀请记录
 * - 查看团队成员列表及详细信息
 * - 查看团队邀请列表及状态管理
 * - 添加新成员（通过邮箱邀请）
 * - 移除团队现有成员
 * - 取消待处理的邀请
 * - 批量操作支持
 * - 成员和邀请搜索筛选
 *
 * 权限控制：
 * - 只有团队创建者可以进行成员管理操作
 * - 创建者不能移除自己
 * - 提供详细的操作确认
 *
 * 界面设计：
 * - 移除标签页导航，采用统一页面布局
 * - 团队成员和邀请记录垂直排列
 * - 提升用户体验和操作效率
 */

import React, { useState, useEffect } from 'react';
import {
  Card,
  Button,
  Space,
  Input,
  Tag,
  Typography,
  Popconfirm,
  Select,
  Tooltip,
  Row,
  Col
} from 'antd';
import { ProTable } from '@ant-design/pro-components';
import {
  DeleteOutlined,
  SearchOutlined,
  MailOutlined,
  UserOutlined,
  CrownOutlined
} from '@ant-design/icons';
import type { ProColumns } from '@ant-design/pro-components';
import dayjs from 'dayjs';

// 导入服务和类型
import { TeamService } from '@/services/team';
import { InvitationService } from '@/services/invitation';
import type { TeamDetailResponse, TeamMemberResponse, TeamInvitationResponse } from '@/types/api';
import { InvitationStatus } from '@/types/api';
import InvitationStatusComponent from '@/components/InvitationStatus';

const { Text } = Typography;

interface TeamMemberManagementProps {
  teamDetail: TeamDetailResponse;
  onRefresh: () => void;
}

const TeamMemberManagement: React.FC<TeamMemberManagementProps> = ({
  teamDetail,
  onRefresh
}) => {
  const [loading, setLoading] = useState(false);
  const [members, setMembers] = useState<TeamMemberResponse[]>([]);
  const [searchText, setSearchText] = useState('');
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);

  // 邀请管理相关状态
  const [invitations, setInvitations] = useState<TeamInvitationResponse[]>([]);
  const [invitationLoading, setInvitationLoading] = useState(false);
  const [invitationSearchText, setInvitationSearchText] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('');

  useEffect(() => {
    fetchMembers();
    fetchInvitations();
  }, []);

  const fetchMembers = async () => {
    try {
      setLoading(true);
      const memberList = await TeamService.getCurrentTeamMembers();
      setMembers(memberList || []);
    } catch (error) {
      setMembers([]); // 确保在错误时设置为空数组
    } finally {
      setLoading(false);
    }
  };

  // 获取邀请列表
  const fetchInvitations = async () => {
    try {
      setInvitationLoading(true);
      const invitationList = await InvitationService.getCurrentTeamInvitations();
      setInvitations(invitationList || []);
    } catch (error) {
      setInvitations([]);
    } finally {
      setInvitationLoading(false);
    }
  };



  // 取消邀请
  const handleCancelInvitation = async (invitationId: number) => {
    try {
      await InvitationService.cancelInvitation(invitationId);
      fetchInvitations();
      onRefresh();
    } catch (error) {
      // 错误处理由响应拦截器统一处理
    }
  };

  // 移除单个成员
  const handleRemoveMember = async (member: TeamMemberResponse) => {
    try {
      await TeamService.removeMember(member.id);
      fetchMembers();
      onRefresh();
    } catch (error) {
      // 错误处理由响应拦截器统一处理
    }
  };

  // 批量移除成员
  const handleBatchRemove = async () => {
    try {
      const memberIds = selectedRowKeys as number[];
      for (const memberId of memberIds) {
        await TeamService.removeMember(memberId);
      }
      setSelectedRowKeys([]);
      fetchMembers();
      onRefresh();
    } catch (error) {
      // 错误处理由响应拦截器统一处理
    }
  };

  // 筛选成员
  const filteredMembers = (members || []).filter(member =>
    member.name.toLowerCase().includes(searchText.toLowerCase()) ||
    member.email.toLowerCase().includes(searchText.toLowerCase())
  );

  // 停用/启用成员
  const handleToggleMemberStatus = async (member: TeamMemberResponse, isActive: boolean) => {
    try {
      await TeamService.updateMemberStatus(member.id, isActive);
      fetchMembers();
      onRefresh();
    } catch (error) {
      // 错误处理由响应拦截器统一处理
    }
  };

  // 表格列配置
  const columns: ProColumns<TeamMemberResponse>[] = [
   
    {
      title: '邮箱',
      dataIndex: 'email',
      key: 'email',
      render: (email: string) => (
        <Text type="secondary">{email}</Text>
      ),
    },
    {
      title: '状态',
      dataIndex: 'isActive',
      key: 'status',
      width: 100,
      render: (isActive: boolean) => (
        <Tag color={isActive ? 'green' : 'red'}>
          {isActive ? '启用' : '停用'}
        </Tag>
      ),
    },
    {
      title: '加入时间',
      dataIndex: 'assignedAt',
      key: 'assignedAt',
      render: (date: string) => new Date(date).toLocaleDateString(),
    },
    {
      title: '最后访问',
      dataIndex: 'lastAccessTime',
      key: 'lastAccessTime',
      render: (date: string) => new Date(date).toLocaleDateString(),
    },
    {
      title: '操作',
      key: 'action',
      render: (_, record) => {
        if (record.isCreator) {
          return <Text type="secondary">-</Text>;
        }

        return (
          <Space>
            {record.isActive ? (
              <Button
                type="text"
                size="small"
                onClick={() => handleToggleMemberStatus(record, false)}
              >
                停用
              </Button>
            ) : (
              <Button
                type="text"
                size="small"
                onClick={() => handleToggleMemberStatus(record, true)}
              >
                启用
              </Button>
            )}
            <Popconfirm
              title="确认移除成员"
              description={`确定要移除成员 ${record.name} 吗？此操作不可恢复。`}
              onConfirm={() => handleRemoveMember(record)}
              okText="确认"
              cancelText="取消"
              okType="danger"
            >
              <Button
                type="text"
                danger
                size="small"
                icon={<DeleteOutlined />}
              >
                移除
              </Button>
            </Popconfirm>
          </Space>
        );
      },
    },
  ];

  // 行选择配置
  const rowSelection = {
    selectedRowKeys,
    onChange: setSelectedRowKeys,
    getCheckboxProps: (record: TeamMemberResponse) => ({
      disabled: record.isCreator, // 创建者不能被选择
    }),
  };

  // 邀请表格列定义
  const invitationColumns: ProColumns<TeamInvitationResponse>[] = [
    {
      title: '受邀人员',
      key: 'invitee',
      render: (_, record) => (
        <Text>{record.inviteeEmail}</Text>
      ),
    },
    {
      title: '邀请状态',
      dataIndex: 'status',
      key: 'status',
      render: (status, record) => (
        <InvitationStatusComponent
          status={status}
          isExpired={record.isExpired}
        />
      ),
      filters: [
        { text: '待确认', value: InvitationStatus.PENDING },
        { text: '已接受', value: InvitationStatus.ACCEPTED },
        { text: '已拒绝', value: InvitationStatus.REJECTED },
        { text: '已过期', value: InvitationStatus.EXPIRED },
        { text: '已取消', value: InvitationStatus.CANCELLED },
      ],
      onFilter: (value, record) => record.status === value,
    },
    {
      title: '邀请时间',
      dataIndex: 'invitedAt',
      key: 'invitedAt',
      render: (time) => (
        <Tooltip title={dayjs(time).format('YYYY-MM-DD HH:mm:ss')}>
          {dayjs(time).format('MM-DD HH:mm')}
        </Tooltip>
      ),
      sorter: (a, b) => dayjs(a.invitedAt).unix() - dayjs(b.invitedAt).unix(),
    },
    {
      title: '过期时间',
      dataIndex: 'expiresAt',
      key: 'expiresAt',
      render: (time, record) => {
        const isExpired = record.isExpired;
        return (
          <Tooltip title={dayjs(time).format('YYYY-MM-DD HH:mm:ss')}>
            <Text type={isExpired ? 'danger' : 'secondary'}>
              {dayjs(time).format('MM-DD HH:mm')}
            </Text>
          </Tooltip>
        );
      },
    },
    {
      title: '操作',
      key: 'action',
      render: (_, record) => {
        if (record.status === InvitationStatus.PENDING && !record.isExpired) {
          return (
            <Popconfirm
              title="确定要取消这个邀请吗？"
              onConfirm={() => handleCancelInvitation(record.id)}
              okText="确定"
              cancelText="取消"
            >
              <Button size="small" danger icon={<DeleteOutlined />}>
                取消邀请
              </Button>
            </Popconfirm>
          );
        }
        return <Text type="secondary">-</Text>;
      },
    },
  ];

  // 过滤邀请列表
  const filteredInvitations = invitations.filter(invitation => {
    const matchesSearch = !invitationSearchText ||
      invitation.inviteeEmail.toLowerCase().includes(invitationSearchText.toLowerCase()) ||
      (invitation.inviteeName && invitation.inviteeName.toLowerCase().includes(invitationSearchText.toLowerCase()));

    const matchesStatus = !statusFilter || invitation.status === statusFilter;

    return matchesSearch && matchesStatus;
  });

  return (
    <div>
      {/* 成员数量和邀请数量统计 */}
      <Card style={{ marginBottom: 16 }}>
        <Space size="large">
          <div>
            <Text type="secondary">团队成员</Text>
            <div style={{ fontSize: '24px', fontWeight: 'bold', color: '#1890ff' }}>
              {(members || []).length}
            </div>
          </div>
          <div>
            <Text type="secondary">邀请记录</Text>
            <div style={{ fontSize: '24px', fontWeight: 'bold', color: '#52c41a' }}>
              {(invitations || []).length}
            </div>
          </div>
        </Space>
      </Card>

      {/* 团队成员和邀请记录并排布局 */}
      <Row gutter={[16, 16]}>
        {/* 第1列：团队成员 */}
        <Col xs={24} lg={12}>
          <Card
            title={
              <Space>
                <UserOutlined />
                <span>团队成员</span>
              </Space>
            }
          >
            {/* 成员操作栏 */}
            <div style={{ marginBottom: 16 }}>
              <Space style={{ width: '100%', justifyContent: 'space-between' }}>
                <Input
                  placeholder="搜索成员姓名或邮箱"
                  prefix={<SearchOutlined />}
                  value={searchText}
                  onChange={(e) => setSearchText(e.target.value)}
                  style={{ width: '100%', maxWidth: 250 }}
                />
                {selectedRowKeys.length > 0 && (
                  <Popconfirm
                    title={`确定要移除选中的 ${selectedRowKeys.length} 名成员吗？`}
                    onConfirm={handleBatchRemove}
                    okText="确定"
                    cancelText="取消"
                  >
                    <Button danger icon={<DeleteOutlined />} size="small">
                      批量移除 ({selectedRowKeys.length})
                    </Button>
                  </Popconfirm>
                )}
              </Space>
            </div>

            {/* 成员列表 */}
            <ProTable
              columns={columns}
              dataSource={filteredMembers}
              rowKey="id"
              loading={loading}
              rowSelection={rowSelection}
              search={{
                labelWidth: 'auto',
                defaultCollapsed: false,
              }}
              toolBarRender={() => [
                <Button
                  key="invite"
                  type="primary"
                  icon={<PlusOutlined />}
                  onClick={() => {/* 邀请成员逻辑 */}}
                >
                  邀请成员
                </Button>,
              ]}
              pagination={{
                showSizeChanger: true,
                showQuickJumper: true,
                showTotal: (total) => `共 ${total} 名成员`,
                pageSize: 5,
                size: 'small'
              }}
              options={{
                search: true,
                reload: true,
                setting: true,
                density: true,
              }}
              size="small"
              headerTitle="团队成员列表"
            />
          </Card>
        </Col>

        {/* 第2列：邀请记录 */}
        <Col xs={24} lg={12}>
          <Card
            title={
              <Space>
                <MailOutlined />
                <span>邀请记录</span>
              </Space>
            }
          >
            {/* 邀请操作栏 - 移除刷新按钮 */}
            <div style={{ marginBottom: 16 }}>
              <Space>
                <Input
                  placeholder="搜索邮箱"
                  prefix={<SearchOutlined />}
                  value={invitationSearchText}
                  onChange={(e) => setInvitationSearchText(e.target.value)}
                  style={{ width: 200 }}
                />
                <Select
                  placeholder="筛选状态"
                  value={statusFilter}
                  onChange={setStatusFilter}
                  style={{ width: 120 }}
                  allowClear
                >
                  <Select.Option value={InvitationStatus.PENDING}>待确认</Select.Option>
                  <Select.Option value={InvitationStatus.ACCEPTED}>已接受</Select.Option>
                  <Select.Option value={InvitationStatus.REJECTED}>已拒绝</Select.Option>
                  <Select.Option value={InvitationStatus.EXPIRED}>已过期</Select.Option>
                  <Select.Option value={InvitationStatus.CANCELLED}>已取消</Select.Option>
                </Select>
              </Space>
            </div>

            {/* 邀请列表 */}
            <ProTable
              columns={invitationColumns}
              dataSource={filteredInvitations}
              rowKey="id"
              loading={invitationLoading}
              pagination={{
                showSizeChanger: true,
                showQuickJumper: true,
                showTotal: (total) => `共 ${total} 条邀请记录`,
                pageSize: 5,
                size: 'small'
              }}
              size="small"
            />
          </Card>
        </Col>
      </Row>


    </div>
  );
};

export default TeamMemberManagement;
