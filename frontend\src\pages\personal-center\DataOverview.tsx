import { BarChartOutlined } from '@ant-design/icons';
import {
  Alert,
  Button,
  Col,
  Row,
  Space,
  Spin,
  Typography,
} from 'antd';
import { ReloadOutlined } from '@ant-design/icons';
import { ProCard } from '@ant-design/pro-components';
import React, { useEffect, useState } from 'react';
import { UserService } from '@/services/user';
import type { UserPersonalStatsResponse } from '@/types/user';

const { Text } = Typography;

/**
 * 数据概览组件
 *
 * 显示用户的个人统计数据，包括车辆、人员、预警、告警等指标。
 * 这是从原UserProfileCard组件中提取的数据概览部分。
 *
 * 主要功能：
 * 1. 显示车辆数量统计
 * 2. 显示人员数量统计
 * 3. 显示预警数量统计
 * 4. 显示告警数量统计
 *
 * 数据来源：
 * - 个人统计数据：通过UserService.getUserPersonalStats()获取
 */
const DataOverview: React.FC = () => {
  /**
   * 个人统计数据状态管理
   */
  const [personalStats, setPersonalStats] = useState<UserPersonalStatsResponse>({
    vehicles: 0,
    personnel: 0,
    warnings: 0,
    alerts: 0,
  });

  const [statsLoading, setStatsLoading] = useState(true);
  const [statsError, setStatsError] = useState<string | null>(null);

  // 获取统计数据
  useEffect(() => {
    const fetchStatsData = async () => {
      try {
        const stats = await UserService.getUserPersonalStats();
        setPersonalStats(stats);
        setStatsError(null);
      } catch (error) {
        console.error('获取统计数据失败:', error);
        setStatsError('获取统计数据失败，请稍后重试');
      } finally {
        setStatsLoading(false);
      }
    };

    fetchStatsData();
  }, []);

  return (
    <ProCard
      title={
        <Space align="center">
          <BarChartOutlined style={{ fontSize: 16, color: '#1890ff' }} />
          <span>数据概览</span>
        </Space>
      }
      extra={
        <Button
          type="text"
          icon={<ReloadOutlined />}
          onClick={() => window.location.reload()}
          size="small"
        >
          刷新
        </Button>
      }
      style={{
        marginBottom: 16,
        borderRadius: 8,
      }}
      headStyle={{
        borderBottom: '1px solid #f0f0f0',
        paddingBottom: 12,
      }}
      bodyStyle={{
        padding: '16px',
      }}
      hoverable
      loading={statsLoading}
    >
      {statsError ? (
        <Alert
          message="数据概览加载失败"
          description={statsError}
          type="error"
          showIcon
        />
      ) : (
        <Spin spinning={statsLoading}>
          <Row gutter={[8, 8]}>
            {/* 车辆统计 */}
            <Col xs={12} sm={6} md={6} lg={6} xl={6}>
              <div style={{ textAlign: 'center', padding: '16px 8px' }}>
                <div
                  style={{
                    fontSize: 28,
                    fontWeight: 700,
                    color: '#1890ff',
                    lineHeight: 1,
                    marginBottom: 6,
                  }}
                >
                  {personalStats.vehicles}
                </div>
                <div
                  style={{
                    fontSize: 13,
                    color: '#8c8c8c',
                    fontWeight: 500,
                  }}
                >
                  车辆
                </div>
              </div>
            </Col>

            {/* 人员统计 */}
            <Col xs={12} sm={6} md={6} lg={6} xl={6}>
              <div style={{ textAlign: 'center', padding: '16px 8px' }}>
                <div
                  style={{
                    fontSize: 28,
                    fontWeight: 700,
                    color: '#52c41a',
                    lineHeight: 1,
                    marginBottom: 6,
                  }}
                >
                  {personalStats.personnel}
                </div>
                <div
                  style={{
                    fontSize: 13,
                    color: '#8c8c8c',
                    fontWeight: 500,
                  }}
                >
                  人员
                </div>
              </div>
            </Col>

            {/* 预警统计 */}
            <Col xs={12} sm={6} md={6} lg={6} xl={6}>
              <div style={{ textAlign: 'center', padding: '16px 8px' }}>
                <div
                  style={{
                    fontSize: 28,
                    fontWeight: 700,
                    color: '#faad14',
                    lineHeight: 1,
                    marginBottom: 6,
                  }}
                >
                  {personalStats.warnings}
                </div>
                <div
                  style={{
                    fontSize: 13,
                    color: '#8c8c8c',
                    fontWeight: 500,
                  }}
                >
                  预警
                </div>
              </div>
            </Col>

            {/* 告警统计 */}
            <Col xs={12} sm={6} md={6} lg={6} xl={6}>
              <div style={{ textAlign: 'center', padding: '16px 8px' }}>
                <div
                  style={{
                    fontSize: 28,
                    fontWeight: 700,
                    color: '#ff4d4f',
                    lineHeight: 1,
                    marginBottom: 6,
                  }}
                >
                  {personalStats.alerts}
                </div>
                <div
                  style={{
                    fontSize: 13,
                    color: '#8c8c8c',
                    fontWeight: 500,
                  }}
                >
                  告警
                </div>
              </div>
            </Col>
          </Row>
        </Spin>
      )}
    </ProCard>
  );
};

export default DataOverview;
