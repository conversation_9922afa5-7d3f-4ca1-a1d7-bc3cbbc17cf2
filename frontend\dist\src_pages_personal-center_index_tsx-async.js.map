{"version": 3, "sources": ["src/pages/personal-center/DataOverview.tsx", "src/pages/personal-center/PersonalInfo.tsx", "src/pages/personal-center/TeamListCard.tsx", "src/pages/personal-center/TodoManagement.tsx", "src/pages/personal-center/UnifiedSettingsModal.tsx", "src/pages/personal-center/index.tsx", "src/services/todo.ts", "src/utils/teamSelectionUtils.ts"], "sourcesContent": ["import { BarChartOutlined } from '@ant-design/icons';\nimport {\n  Alert,\n  Button,\n  Col,\n  Row,\n  Space,\n  Spin,\n  Typography,\n} from 'antd';\nimport { ReloadOutlined } from '@ant-design/icons';\nimport { ProCard } from '@ant-design/pro-components';\nimport React, { useEffect, useState } from 'react';\nimport { UserService } from '@/services/user';\nimport type { UserPersonalStatsResponse } from '@/types/user';\n\nconst { Text } = Typography;\n\n/**\n * 数据概览组件\n *\n * 显示用户的个人统计数据，包括车辆、人员、预警、告警等指标。\n * 这是从原UserProfileCard组件中提取的数据概览部分。\n *\n * 主要功能：\n * 1. 显示车辆数量统计\n * 2. 显示人员数量统计\n * 3. 显示预警数量统计\n * 4. 显示告警数量统计\n *\n * 数据来源：\n * - 个人统计数据：通过UserService.getUserPersonalStats()获取\n */\nconst DataOverview: React.FC = () => {\n  /**\n   * 个人统计数据状态管理\n   */\n  const [personalStats, setPersonalStats] = useState<UserPersonalStatsResponse>({\n    vehicles: 0,\n    personnel: 0,\n    warnings: 0,\n    alerts: 0,\n  });\n\n  const [statsLoading, setStatsLoading] = useState(true);\n  const [statsError, setStatsError] = useState<string | null>(null);\n\n  // 获取统计数据\n  useEffect(() => {\n    const fetchStatsData = async () => {\n      try {\n        const stats = await UserService.getUserPersonalStats();\n        setPersonalStats(stats);\n        setStatsError(null);\n      } catch (error) {\n        console.error('获取统计数据失败:', error);\n        setStatsError('获取统计数据失败，请稍后重试');\n      } finally {\n        setStatsLoading(false);\n      }\n    };\n\n    fetchStatsData();\n  }, []);\n\n  return (\n    <ProCard\n      title={\n        <Space align=\"center\">\n          <BarChartOutlined style={{ fontSize: 16, color: '#1890ff' }} />\n          <span>数据概览</span>\n        </Space>\n      }\n      extra={\n        <Button\n          type=\"text\"\n          icon={<ReloadOutlined />}\n          onClick={() => window.location.reload()}\n          size=\"small\"\n        >\n          刷新\n        </Button>\n      }\n      style={{\n        marginBottom: 16,\n        borderRadius: 8,\n      }}\n      headStyle={{\n        borderBottom: '1px solid #f0f0f0',\n        paddingBottom: 12,\n      }}\n      bodyStyle={{\n        padding: '16px',\n      }}\n      hoverable\n      loading={statsLoading}\n    >\n      {statsError ? (\n        <Alert\n          message=\"数据概览加载失败\"\n          description={statsError}\n          type=\"error\"\n          showIcon\n        />\n      ) : (\n        <Spin spinning={statsLoading}>\n          <Row gutter={[8, 8]}>\n            {/* 车辆统计 */}\n            <Col xs={12} sm={6} md={6} lg={6} xl={6}>\n              <div style={{ textAlign: 'center', padding: '16px 8px' }}>\n                <div\n                  style={{\n                    fontSize: 28,\n                    fontWeight: 700,\n                    color: '#1890ff',\n                    lineHeight: 1,\n                    marginBottom: 6,\n                  }}\n                >\n                  {personalStats.vehicles}\n                </div>\n                <div\n                  style={{\n                    fontSize: 13,\n                    color: '#8c8c8c',\n                    fontWeight: 500,\n                  }}\n                >\n                  车辆\n                </div>\n              </div>\n            </Col>\n\n            {/* 人员统计 */}\n            <Col xs={12} sm={6} md={6} lg={6} xl={6}>\n              <div style={{ textAlign: 'center', padding: '16px 8px' }}>\n                <div\n                  style={{\n                    fontSize: 28,\n                    fontWeight: 700,\n                    color: '#52c41a',\n                    lineHeight: 1,\n                    marginBottom: 6,\n                  }}\n                >\n                  {personalStats.personnel}\n                </div>\n                <div\n                  style={{\n                    fontSize: 13,\n                    color: '#8c8c8c',\n                    fontWeight: 500,\n                  }}\n                >\n                  人员\n                </div>\n              </div>\n            </Col>\n\n            {/* 预警统计 */}\n            <Col xs={12} sm={6} md={6} lg={6} xl={6}>\n              <div style={{ textAlign: 'center', padding: '16px 8px' }}>\n                <div\n                  style={{\n                    fontSize: 28,\n                    fontWeight: 700,\n                    color: '#faad14',\n                    lineHeight: 1,\n                    marginBottom: 6,\n                  }}\n                >\n                  {personalStats.warnings}\n                </div>\n                <div\n                  style={{\n                    fontSize: 13,\n                    color: '#8c8c8c',\n                    fontWeight: 500,\n                  }}\n                >\n                  预警\n                </div>\n              </div>\n            </Col>\n\n            {/* 告警统计 */}\n            <Col xs={12} sm={6} md={6} lg={6} xl={6}>\n              <div style={{ textAlign: 'center', padding: '16px 8px' }}>\n                <div\n                  style={{\n                    fontSize: 28,\n                    fontWeight: 700,\n                    color: '#ff4d4f',\n                    lineHeight: 1,\n                    marginBottom: 6,\n                  }}\n                >\n                  {personalStats.alerts}\n                </div>\n                <div\n                  style={{\n                    fontSize: 13,\n                    color: '#8c8c8c',\n                    fontWeight: 500,\n                  }}\n                >\n                  告警\n                </div>\n              </div>\n            </Col>\n          </Row>\n        </Spin>\n      )}\n    </ProCard>\n  );\n};\n\nexport default DataOverview;\n", "import {\n  ClockCircleOutlined,\n  MailOutlined,\n  PhoneOutlined,\n  SettingOutlined,\n  TeamOutlined,\n  UserOutlined,\n} from '@ant-design/icons';\nimport {\n  Alert,\n  Avatar,\n  Button,\n  Col,\n  Dropdown,\n  Flex,\n  Row,\n  Space,\n  Spin,\n  Typography,\n} from 'antd';\nimport { ProCard } from '@ant-design/pro-components';\nimport React, { useEffect, useState } from 'react';\nimport { UserService } from '@/services/user';\nimport type { UserProfileDetailResponse } from '@/types/user';\nimport UnifiedSettingsModal from './UnifiedSettingsModal';\n\nconst { Title, Text } = Typography;\n\n/**\n * 个人信息组件\n *\n * 显示用户的完整个人信息，包括基本信息和登录历史。\n * 整合了原UserProfileCard和LastLoginInfo组件的功能。\n *\n * 主要功能：\n * 1. 显示用户头像（使用姓名首字母）\n * 2. 显示用户姓名、邮箱、电话\n * 3. 显示注册日期\n * 4. 显示最后登录时间和登录团队\n *\n * 数据来源：\n * - 用户详细信息：通过UserService.getUserProfileDetail()获取\n */\nconst PersonalInfo: React.FC = () => {\n  /**\n   * 用户详细信息状态管理\n   */\n  const [userInfo, setUserInfo] = useState<UserProfileDetailResponse>({\n    name: '',\n    position: '',\n    email: '',\n    telephone: '',\n    registerDate: '',\n    lastLoginTime: '',\n    lastLoginTeam: '',\n    teamCount: 0,\n    avatar: '',\n  });\n\n  const [userInfoLoading, setUserInfoLoading] = useState(true);\n  const [userInfoError, setUserInfoError] = useState<string | null>(null);\n\n  // Modal状态管理\n  const [settingsModalVisible, setSettingsModalVisible] = useState(false);\n\n  // 获取用户数据\n  useEffect(() => {\n    const fetchUserData = async () => {\n      try {\n        const userDetail = await UserService.getUserProfileDetail();\n        setUserInfo(userDetail);\n        setUserInfoError(null);\n      } catch (error) {\n        console.error('获取用户详细信息失败:', error);\n        setUserInfoError('获取用户详细信息失败，请稍后重试');\n      } finally {\n        setUserInfoLoading(false);\n      }\n    };\n\n    fetchUserData();\n  }, []);\n\n  return (\n    <ProCard\n      title=\"个人信息\"\n      style={{\n        marginBottom: 16,\n        borderRadius: 8,\n      }}\n      // styles={{\n      //   header: {\n      //     borderBottom: '1px solid #f0f0f0',\n      //     paddingBottom: 12,\n      //   },\n      //   body: {\n      //     padding: '16px',\n      //   },\n      // }}\n    >\n      {userInfoError ? (\n        <Alert\n          message=\"个人信息加载失败\"\n          description={userInfoError}\n          type=\"error\"\n          showIcon\n        />\n      ) : (\n        <Spin spinning={userInfoLoading}>\n          {/* 明信片式紧凑布局 */}\n          <div>\n            <Row gutter={[16, 0]} align=\"middle\">\n              {/* 左侧：头像区域 */}\n              <Col xs={24} sm={6} md={5} lg={4} xl={4}>\n                <Flex justify=\"center\" align=\"center\" style={{ height: '100%' }}>\n                  <Avatar\n                    size={56}\n                    shape=\"circle\"\n                    style={{\n                      backgroundColor: '#1890ff',\n                      fontSize: 20,\n                      fontWeight: 600,\n                      border: '2px solid #e6f4ff',\n                      boxShadow: '0 2px 8px rgba(24, 144, 255, 0.12)',\n                    }}\n                  >\n                    {userInfo.name ? (\n                      userInfo.name.charAt(0).toUpperCase()\n                    ) : (\n                      <UserOutlined />\n                    )}\n                  </Avatar>\n                </Flex>\n              </Col>\n\n              {/* 右侧：用户信息区域 */}\n              <Col xs={24} sm={18} md={19} lg={20} xl={20}>\n                <div style={{ paddingLeft: '12px' }}>\n                  {/* 第一行：姓名和操作按钮 */}\n                  <div style={{ marginBottom: '8px', display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>\n                    <Title\n                      level={4}\n                      style={{\n                        margin: 0,\n                        fontSize: 18,\n                        fontWeight: 600,\n                        color: '#262626',\n                        lineHeight: 1.3,\n                      }}\n                    >\n                      {userInfo.name || '加载中...'}\n                    </Title>\n\n                    {/* 操作按钮区域 */}\n                    <Button\n                      size=\"small\"\n                      icon={<SettingOutlined />}\n                      onClick={() => setSettingsModalVisible(true)}\n                      style={{\n                        borderRadius: 6,\n                        width: 28,\n                        height: 28,\n                        padding: 0,\n                        border: '1px solid #d9d9d9',\n                        color: '#595959',\n                        display: 'flex',\n                        alignItems: 'center',\n                        justifyContent: 'center',\n                      }}\n                    />\n                  </div>\n\n                  {/* 第二行：联系信息 */}\n                  <div style={{ marginBottom: '8px' }}>\n                    <Space size={16} wrap>\n                      {userInfo.email && (\n                        <Space size={6} align=\"center\">\n                          <MailOutlined\n                            style={{\n                              fontSize: 14,\n                              color: '#1890ff',\n                            }}\n                          />\n                          <Text\n                            style={{\n                              color: '#595959',\n                              fontSize: 13,\n                              fontWeight: 500,\n                            }}\n                          >\n                            {userInfo.email}\n                          </Text>\n                        </Space>\n                      )}\n                      {userInfo.telephone && (\n                        <Space size={6} align=\"center\">\n                          <PhoneOutlined\n                            style={{\n                              fontSize: 14,\n                              color: '#52c41a',\n                            }}\n                          />\n                          <Text\n                            style={{\n                              color: '#595959',\n                              fontSize: 13,\n                              fontWeight: 500,\n                            }}\n                          >\n                            {userInfo.telephone}\n                          </Text>\n                        </Space>\n                      )}\n                    </Space>\n                  </div>\n\n                  {/* 第三行：注册日期和登录信息 */}\n                  <div>\n                    <Space size={16} wrap>\n                      {/* 注册日期 */}\n                      {userInfo.registerDate && (\n                        <Space size={4} align=\"center\">\n                          <Text\n                            style={{\n                              fontSize: 12,\n                              color: '#8c8c8c',\n                              fontWeight: 500,\n                            }}\n                          >\n                            📅 注册于 {userInfo.registerDate}\n                          </Text>\n                        </Space>\n                      )}\n\n                      {/* 最后登录时间 */}\n                      {userInfo.lastLoginTime && (\n                        <Space size={4} align=\"center\">\n                          <ClockCircleOutlined\n                            style={{\n                              fontSize: 12,\n                              color: '#1890ff',\n                            }}\n                          />\n                          <Text\n                            style={{\n                              fontSize: 12,\n                              color: '#8c8c8c',\n                              fontWeight: 500,\n                            }}\n                          >\n                            最后登录：{userInfo.lastLoginTime}\n                          </Text>\n                        </Space>\n                      )}\n\n                      {/* 最后登录团队 */}\n                      {userInfo.lastLoginTeam && (\n                        <Space size={4} align=\"center\">\n                          <TeamOutlined\n                            style={{\n                              fontSize: 12,\n                              color: '#52c41a',\n                            }}\n                          />\n                          <Text\n                            style={{\n                              fontSize: 12,\n                              color: '#8c8c8c',\n                              fontWeight: 500,\n                            }}\n                          >\n                            团队：{userInfo.lastLoginTeam}\n                          </Text>\n                        </Space>\n                      )}\n                    </Space>\n                  </div>\n                </div>\n              </Col>\n            </Row>\n          </div>\n        </Spin>\n      )}\n\n      {/* 统一设置Modal */}\n      <UnifiedSettingsModal\n        visible={settingsModalVisible}\n        onCancel={() => setSettingsModalVisible(false)}\n        userInfo={userInfo}\n        onSuccess={() => {\n          // 可以在这里刷新用户信息或团队列表\n          console.log('设置操作成功');\n        }}\n      />\n    </ProCard>\n  );\n};\n\nexport default PersonalInfo;\n", "import {\n  CarOutlined,\n  CheckCircleOutlined,\n  ClockCircleOutlined,\n  CrownOutlined,\n  ExclamationCircleOutlined,\n  MinusCircleOutlined,\n  RightOutlined,\n  TeamOutlined,\n  UserOutlined,\n} from '@ant-design/icons';\nimport { history, useModel } from '@umijs/max';\nimport {\n  <PERSON><PERSON>,\n  Button,\n  Col,\n  Flex,\n  message,\n  Row,\n  Spin,\n  Tooltip,\n  Typography,\n} from 'antd';\nimport { ProCard, ProList } from '@ant-design/pro-components';\nimport React, { useEffect, useState } from 'react';\nimport { AuthService } from '@/services';\nimport { TeamService } from '@/services/team';\nimport type { TeamDetailResponse } from '@/types/api';\nimport {\n  getTeamIdFromCurrentToken,\n  hasTeamInCurrentToken,\n  getUserIdFromCurrentToken,\n} from '@/utils/tokenUtils';\nimport { recordTeamSelection, hasUserSelectedTeam } from '@/utils/teamSelectionUtils';\n\nconst { Text, Title } = Typography;\n \n\n\n\n// 响应式布局样式\nconst styles = `\n  .team-item .ant-pro-card-body {\n    padding: 0 !important;\n  }\n\n  .team-item:hover {\n    transform: translateY(-1px);\n    box-shadow: 0 4px 12px rgba(0,0,0,0.1) !important;\n  }\n\n  @media (max-width: 768px) {\n    .team-item {\n      margin-bottom: 8px;\n    }\n\n    .team-stats-row {\n      margin-top: 8px;\n    }\n\n    .team-info-wrap {\n      gap: 8px !important;\n    }\n  }\n\n  @media (max-width: 576px) {\n    .team-stats-row {\n      margin-top: 12px;\n    }\n\n    .team-stats-col {\n      margin-bottom: 4px;\n    }\n\n    .team-info-wrap {\n      gap: 6px !important;\n    }\n\n    .team-meta-info {\n      flex-wrap: wrap;\n      gap: 6px !important;\n    }\n\n    .team-status-badges {\n      flex-wrap: wrap;\n      gap: 4px !important;\n      margin-top: 4px;\n    }\n  }\n\n  @media (max-width: 480px) {\n    .team-name-text {\n      font-size: 14px !important;\n    }\n\n    .team-meta-text {\n      font-size: 11px !important;\n    }\n\n    .team-meta-info {\n      gap: 4px !important;\n    }\n\n    .team-status-badges {\n      gap: 3px !important;\n    }\n  }\n`;\n\n/**\n * 团队列表卡片组件\n *\n * 这是个人中心页面的核心组件，负责显示用户所属的团队列表，\n * 并提供团队切换、创建团队等功能。是团队管理系统的重要入口。\n *\n * 主要功能：\n * 1. 显示用户所属的所有团队\n * 2. 支持团队切换功能\n * 3. 支持创建新团队\n * 4. 显示当前选择的团队状态\n * 5. 处理团队切换过程中的状态管理\n *\n * 状态管理：\n * - 团队列表数据的获取和显示\n * - 团队切换过程的加载状态\n * - 创建团队模态框的状态\n * - 错误状态的处理和显示\n *\n * 团队切换逻辑：\n * 1. 检查用户登录状态\n * 2. 判断是否为当前团队（避免重复切换）\n * 3. 调用后端API进行团队切换\n * 4. 更新本地Token和全局状态\n * 5. 跳转到团队仪表盘\n *\n * 与全局状态的集成：\n * - 监听用户登录状态变化\n * - 同步团队切换后的状态更新\n * - 处理用户注销时的状态清理\n */\nconst TeamListCard: React.FC = () => {\n  /**\n   * 团队列表相关状态管理\n   *\n   * 这些状态用于管理团队列表的显示和交互：\n   * - teams: 用户所属的团队列表数据\n   * - loading: 团队列表加载状态\n   * - error: 错误信息（如网络错误、权限错误等）\n   * - switchingTeamId: 当前正在切换的团队ID（用于显示加载状态）\n   */\n  const [teams, setTeams] = useState<TeamDetailResponse[]>([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState<string | null>(null);\n  const [switchingTeamId, setSwitchingTeamId] = useState<number | null>(null);\n\n  /**\n   * 创建团队功能已移至设置页面\n   *\n   * 为了更好的用户体验和功能组织，创建团队功能已经移动到\n   * 专门的设置页面中。用户可以通过\"团队设置\"按钮跳转到\n   * 设置页面进行团队创建和管理操作。\n   */\n\n  /**\n   * 全局状态管理\n   *\n   * 从UmiJS全局状态中获取用户和团队信息：\n   * - initialState: 包含当前用户和团队信息的全局状态\n   * - setInitialState: 更新全局状态的函数\n   * - currentTeam: 当前选择的团队信息\n   */\n  const { initialState, setInitialState } = useModel('@@initialState');\n  const currentTeam = initialState?.currentTeam;\n\n  /**\n   * Token信息提取\n   *\n   * 从当前存储的Token中提取关键信息，用于状态判断和权限检查：\n   * - currentTokenTeamId: Token中包含的团队ID\n   * - currentUserId: Token中包含的用户ID\n   * - hasTeamInToken: Token是否包含团队信息\n   *\n   * 这些信息用于：\n   * - 判断当前是否已选择团队\n   * - 确定哪个团队是当前激活的团队\n   * - 记录用户的团队选择历史\n   */\n  const currentTokenTeamId = getTeamIdFromCurrentToken();\n  const currentUserId = getUserIdFromCurrentToken();\n  const hasTeamInToken = hasTeamInCurrentToken();\n\n  // 判断是否有真正的当前团队：\n  // 1. Token中有团队信息（说明用户已经选择过团队）\n  // 2. initialState中有团队信息（说明已经获取过团队详情）\n  // 3. 两者的团队ID一致（确保状态同步）\n  // 4. 用户曾经主动选择过这个团队（区分初始登录和主动选择）\n  const hasRealCurrentTeam = !!(\n    hasTeamInToken &&\n    currentTokenTeamId &&\n    currentTeam &&\n    currentTeam.id === currentTokenTeamId &&\n    currentUserId &&\n    hasUserSelectedTeam(currentUserId, currentTokenTeamId)\n  );\n\n  // 获取实际的当前团队ID：只有在用户真正选择过团队且状态同步时才返回团队ID\n  const actualCurrentTeamId = hasRealCurrentTeam ? currentTokenTeamId : null;\n\n  // 调试日志\n  console.log('TeamListCard 状态调试:', {\n    currentTeam: currentTeam?.id,\n    currentTokenTeamId,\n    currentUserId,\n    hasTeamInToken,\n    hasRealCurrentTeam,\n    actualCurrentTeamId,\n    hasUserSelectedCurrentTeam: currentUserId && currentTokenTeamId ? hasUserSelectedTeam(currentUserId, currentTokenTeamId) : false,\n    initialStateCurrentUser: !!initialState?.currentUser,\n  });\n\n  // 获取团队列表数据\n  useEffect(() => {\n    const fetchTeams = async () => {\n      try {\n        setLoading(true);\n        setError(null);\n        const teamsData = await TeamService.getUserTeamsWithStats();\n        setTeams(teamsData);\n      } catch (error) {\n        console.error('获取团队列表失败:', error);\n        setError('获取团队列表失败');\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    // 只有在用户已登录时才获取团队列表\n    if (initialState?.currentUser) {\n      fetchTeams();\n    }\n  }, [initialState?.currentUser]);\n\n  // 监听全局状态变化，处理注销等情况\n  useEffect(() => {\n    // 如果用户已注销（currentUser为undefined），清除本地团队列表状态\n    if (!initialState?.currentUser) {\n      setTeams([]);\n      setError(null);\n      setLoading(false);\n      setSwitchingTeamId(null);\n    }\n  }, [initialState?.currentUser]);\n\n  // 监听当前团队状态变化\n  useEffect(() => {\n    console.log('当前团队状态变化:', {\n      currentTeam: currentTeam?.id,\n      actualCurrentTeamId,\n      hasRealCurrentTeam,\n    });\n  }, [currentTeam?.id, actualCurrentTeamId, hasRealCurrentTeam]);\n\n  // 创建团队功能已移至设置页面，此处不再需要处理函数\n\n  /**\n   * 团队切换处理函数\n   *\n   * 这是团队切换功能的核心函数，处理用户从一个团队切换到另一个团队的完整流程。\n   * 包括权限检查、API调用、状态更新、页面跳转等步骤。\n   *\n   * 切换流程：\n   * 1. 用户登录状态检查\n   * 2. 当前团队状态判断（避免重复切换）\n   * 3. 调用后端团队选择API\n   * 4. 验证切换结果\n   * 5. 更新本地Token和全局状态\n   * 6. 记录用户选择历史\n   * 7. 跳转到团队仪表盘\n   *\n   * 状态管理：\n   * - 设置切换加载状态（防止重复点击）\n   * - 更新全局用户和团队状态\n   * - 处理切换过程中的错误状态\n   *\n   * 错误处理：\n   * - 网络错误：显示网络连接提示\n   * - 权限错误：由响应拦截器统一处理\n   * - 业务错误：显示具体的错误信息\n   *\n   * @param teamId 要切换到的团队ID\n   * @param teamName 团队名称（用于显示消息）\n   */\n  const handleTeamSwitch = async (teamId: number, teamName: string) => {\n    /**\n     * 用户登录状态检查\n     *\n     * 确保用户已登录才能进行团队切换操作。\n     * 虽然组件层面已有登录检查，但这里再次确认以确保安全性。\n     */\n    if (!initialState?.currentUser) {\n      return;\n    }\n\n    try {\n      /**\n       * 设置切换状态\n       *\n       * 标记当前正在切换的团队ID，用于：\n       * 1. 在UI上显示加载状态\n       * 2. 防止用户重复点击\n       * 3. 提供视觉反馈\n       */\n      setSwitchingTeamId(teamId);\n\n      /**\n       * 当前团队检查\n       *\n       * 如果用户点击的是当前已选择的团队，直接跳转到仪表盘，\n       * 避免不必要的API调用和Token更新。\n       */\n      if (teamId === actualCurrentTeamId) {\n        history.push('/dashboard');\n        return;\n      }\n\n      /**\n       * 执行团队切换API调用\n       *\n       * 调用后端的团队选择接口，后端会：\n       * 1. 验证用户是否有权限访问该团队\n       * 2. 生成包含新团队信息的JWT Token\n       * 3. 返回团队详细信息和切换状态\n       */\n      const response = await AuthService.selectTeam({ teamId });\n\n      /**\n       * 验证切换结果\n       *\n       * 检查后端返回的响应是否表示切换成功：\n       * - teamSelectionSuccess: 切换成功标识\n       * - team: 新团队的详细信息\n       * - team.id: 确认返回的团队ID与请求的一致\n       */\n      if (\n        response.teamSelectionSuccess &&\n        response.team &&\n        response.team.id === teamId\n      ) {\n        /**\n         * 记录用户选择历史\n         *\n         * 将用户的团队选择记录到本地存储，用于：\n         * - 下次登录时的默认团队选择\n         * - 用户行为分析\n         * - 提升用户体验\n         */\n        if (currentUserId) {\n          recordTeamSelection(currentUserId, teamId);\n        }\n\n        /**\n         * 异步更新全局状态\n         *\n         * 由于Token已经更新，需要同步更新全局状态中的用户和团队信息。\n         * 使用异步更新避免阻塞页面跳转，提升用户体验。\n         *\n         * 更新流程：\n         * 1. 并行获取最新的用户信息和团队信息\n         * 2. 验证获取的团队信息是否正确\n         * 3. 更新全局状态\n         * 4. 处理更新过程中的错误\n         */\n        if (\n          initialState?.fetchTeamInfo &&\n          initialState?.fetchUserInfo &&\n          setInitialState\n        ) {\n          // 异步更新状态，不阻塞跳转\n          Promise.all([\n            initialState.fetchUserInfo(),\n            initialState.fetchTeamInfo(),\n          ])\n            .then(([currentUser, currentTeam]) => {\n              // 确认获取的团队信息与切换的团队一致\n              if (currentTeam && currentTeam.id === teamId) {\n                setInitialState({\n                  ...initialState,\n                  currentUser,\n                  currentTeam,\n                });\n              }\n            })\n            .catch((error) => {\n              console.error('更新 initialState 失败:', error);\n              // 状态更新失败不影响团队切换的核心功能\n            });\n        }\n\n        /**\n         * 页面跳转\n         *\n         * 切换成功后跳转到团队仪表盘。\n         * 路由守卫会验证新的Token并允许访问团队页面。\n         */\n        history.push('/dashboard');\n      } else {\n        /**\n         * 切换失败处理\n         *\n         * 如果后端返回的响应不符合预期，说明切换失败。\n         * 记录错误日志并提示用户重试。\n         */\n        // 团队切换响应异常，未返回正确的团队信息\n      }\n    } catch (error: any) {\n      /**\n       * 异常处理\n       *\n       * 处理团队切换过程中可能出现的各种异常：\n       * - 网络错误：连接超时、服务器不可达等\n       * - 权限错误：用户无权限访问该团队\n       * - 业务错误：团队不存在、状态异常等\n       *\n       * 错误处理策略：\n       * 1. 记录详细的错误日志用于调试\n       * 2. 响应拦截器已处理大部分错误消息\n       * 3. 只对网络错误显示通用提示\n       */\n      // 错误处理由响应拦截器统一处理\n    } finally {\n      /**\n       * 清理切换状态\n       *\n       * 无论切换成功还是失败，都要清除切换状态，\n       * 恢复UI的正常状态，允许用户进行下一次操作。\n       */\n      setSwitchingTeamId(null);\n    }\n  };\n\n  return (\n    <>\n      {/* 注入样式 */}\n      <style dangerouslySetInnerHTML={{ __html: styles }} />\n\n      <ProCard\n        title=\"团队列表\"\n        style={{\n          borderRadius: 8,\n          marginBottom: 16,\n        }}\n        headStyle={{\n          borderBottom: '1px solid #f0f0f0',\n          paddingBottom: 12,\n        }}\n        bodyStyle={{\n          padding: '16px',\n        }}\n      >\n        {error ? (\n          <Alert\n            message=\"团队列表加载失败\"\n            description={error}\n            type=\"error\"\n            showIcon\n            style={{ marginBottom: 16 }}\n          />\n        ) : (\n          <Spin spinning={loading}>\n            {!initialState?.currentUser ? (\n              <div style={{ textAlign: 'center', padding: '40px 20px' }}>\n                <Text type=\"secondary\">请先登录以查看团队列表</Text>\n              </div>\n            ) : teams.length === 0 && !loading ? (\n              <div style={{ textAlign: 'center', padding: '40px 20px' }}>\n                <Text type=\"secondary\">暂无团队，请先加入或创建团队</Text>\n              </div>\n            ) : (\n              <ProList\n                dataSource={teams}\n                renderItem={(item) => (\n                  <ProCard\n                    className=\"team-item\"\n                    style={{\n                        background:\n                          actualCurrentTeamId === item.id\n                            ? 'linear-gradient(135deg, #f0f9ff, #e6f4ff)'\n                            : '#fff',\n                        borderRadius: 8,\n                        boxShadow:\n                          actualCurrentTeamId === item.id\n                            ? '0 2px 8px rgba(24, 144, 255, 0.12)'\n                            : '0 1px 4px rgba(0,0,0,0.06)',\n                        width: '100%',\n                        borderLeft: `3px solid ${item.isCreator ? '#722ed1' : '#52c41a'}`,\n                        transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',\n                        border:\n                          actualCurrentTeamId === item.id\n                            ? '1px solid #91caff'\n                            : '1px solid #f0f0f0',\n                        padding: '12px 16px',\n                        position: 'relative',\n                        overflow: 'hidden',\n                      }}\n                      hoverable\n                      onMouseEnter={(e) => {\n                        if (actualCurrentTeamId !== item.id) {\n                          e.currentTarget.style.transform = 'translateY(-2px)';\n                          e.currentTarget.style.boxShadow =\n                            '0 8px 24px rgba(0,0,0,0.12)';\n                        }\n                      }}\n                      onMouseLeave={(e) => {\n                        if (actualCurrentTeamId !== item.id) {\n                          e.currentTarget.style.transform = 'translateY(0)';\n                          e.currentTarget.style.boxShadow =\n                            '0 2px 8px rgba(0,0,0,0.06)';\n                        }\n                      }}\n                    >\n                      {/* 响应式布局 */}\n                      <Row\n                        gutter={[8, 8]}\n                        align=\"middle\"\n                        style={{ width: '100%' }}\n                      >\n                        {/* 左侧：团队信息 */}\n                        <Col xs={24} sm={24} md={14} lg={12} xl={14}>\n                          <Flex vertical gap={6} className=\"team-info-wrap\">\n                            {/* 团队名称行 */}\n                            <Flex align=\"center\" gap={8} wrap=\"wrap\">\n                              <div\n                                style={{\n                                  cursor: 'pointer',\n                                  padding: '2px 4px',\n                                  borderRadius: 4,\n                                  transition: 'all 0.2s ease',\n                                  display: 'flex',\n                                  alignItems: 'center',\n                                  gap: 6,\n                                }}\n                                onClick={() =>\n                                  handleTeamSwitch(item.id, item.name)\n                                }\n                                onMouseEnter={(e) => {\n                                  e.currentTarget.style.background =\n                                    'rgba(24, 144, 255, 0.05)';\n                                }}\n                                onMouseLeave={(e) => {\n                                  e.currentTarget.style.background =\n                                    'transparent';\n                                }}\n                              >\n                                <Text\n                                  strong\n                                  style={{\n                                    fontSize: 16,\n                                    color:\n                                      actualCurrentTeamId === item.id\n                                        ? '#1890ff'\n                                        : '#262626',\n                                    lineHeight: 1.2,\n                                  }}\n                                >\n                                  {item.name}\n                                </Text>\n                                <RightOutlined\n                                  style={{\n                                    fontSize: 10,\n                                    color:\n                                      actualCurrentTeamId === item.id\n                                        ? '#1890ff'\n                                        : '#8c8c8c',\n                                    verticalAlign: 'middle',\n                                    display: 'inline-flex',\n                                    alignItems: 'center',\n                                  }}\n                                />\n                              </div>\n\n                              {/* 状态标识 */}\n                              {actualCurrentTeamId === item.id && (\n                                <span\n                                  style={{\n                                    background: '#1890ff',\n                                    color: 'white',\n                                    padding: '1px 6px',\n                                    borderRadius: 8,\n                                    fontSize: 10,\n                                    fontWeight: 500,\n                                  }}\n                                >\n                                  当前\n                                </span>\n                              )}\n\n\n\n                              {switchingTeamId === item.id && (\n                                <Flex align=\"center\" gap={4}>\n                                  <Spin size=\"small\" />\n                                  <Text style={{ fontSize: 10, color: '#666' }}>\n                                    切换中\n                                  </Text>\n                                </Flex>\n                              )}\n                            </Flex>\n\n                            {/* 团队基本信息 */}\n                            <Flex align=\"center\" gap={12} wrap=\"wrap\" className=\"team-meta-info\">\n                              <Tooltip\n                                title={`团队创建时间: ${new Date(item.createdAt).toLocaleString('zh-CN')}`}\n                              >\n                                <Flex align=\"center\" gap={4}>\n                                  <ClockCircleOutlined\n                                    style={{ color: '#8c8c8c', fontSize: 12 }}\n                                  />\n                                  <Text\n                                    style={{ fontSize: 12, color: '#8c8c8c' }}\n                                  >\n                                    创建: {new Date(\n                                      item.createdAt,\n                                    ).toLocaleDateString('zh-CN')}\n                                  </Text>\n                                </Flex>\n                              </Tooltip>\n\n                              {/* 加入日期 */}\n                              {item.assignedAt && (\n                                <Tooltip\n                                  title={`加入团队时间: ${new Date(item.assignedAt).toLocaleString('zh-CN')}`}\n                                >\n                                  <Flex align=\"center\" gap={4}>\n                                    <UserOutlined\n                                      style={{ color: '#8c8c8c', fontSize: 12 }}\n                                    />\n                                    <Text\n                                      style={{ fontSize: 12, color: '#8c8c8c' }}\n                                    >\n                                      加入: {new Date(\n                                        item.assignedAt,\n                                      ).toLocaleDateString('zh-CN')}\n                                    </Text>\n                                  </Flex>\n                                </Tooltip>\n                              )}\n\n                              <Tooltip\n                                title={`团队成员: ${item.memberCount}人`}\n                              >\n                                <Flex align=\"center\" gap={4}>\n                                  <TeamOutlined\n                                    style={{ color: '#8c8c8c', fontSize: 12 }}\n                                  />\n                                  <Text\n                                    style={{ fontSize: 12, color: '#8c8c8c' }}\n                                  >\n                                    {item.memberCount} 人\n                                  </Text>\n                                </Flex>\n                              </Tooltip>\n                            </Flex>\n\n                            {/* 状态标识行 */}\n                            <Flex align=\"center\" gap={8} wrap=\"wrap\" className=\"team-status-badges\">\n                              {/* 角色标识 */}\n                              <span\n                                style={{\n                                  background: item.isCreator\n                                    ? '#722ed1'\n                                    : '#52c41a',\n                                  color: 'white',\n                                  padding: '2px 6px',\n                                  borderRadius: 8,\n                                  fontSize: 10,\n                                  fontWeight: 500,\n                                  display: 'flex',\n                                  alignItems: 'center',\n                                  gap: 2,\n                                }}\n                              >\n                                {item.isCreator ? (\n                                  <>\n                                    <CrownOutlined style={{ fontSize: 9 }} />\n                                    管理员\n                                  </>\n                                ) : (\n                                  <>\n                                    <UserOutlined style={{ fontSize: 9 }} />\n                                    成员\n                                  </>\n                                )}\n                              </span>\n\n                              {/* 用户状态标识 */}\n                              <span\n                                style={{\n                                  background: item.isActive ? '#52c41a' : '#ff4d4f',\n                                  color: 'white',\n                                  padding: '2px 6px',\n                                  borderRadius: 8,\n                                  fontSize: 10,\n                                  fontWeight: 500,\n                                  display: 'flex',\n                                  alignItems: 'center',\n                                  gap: 2,\n                                }}\n                              >\n                                {item.isActive ? (\n                                  <>\n                                    <CheckCircleOutlined style={{ fontSize: 9 }} />\n                                    启用\n                                  </>\n                                ) : (\n                                  <>\n                                    <MinusCircleOutlined style={{ fontSize: 9 }} />\n                                    停用\n                                  </>\n                                )}\n                              </span>\n                            </Flex>\n                          </Flex>\n                        </Col>\n\n                        {/* 右侧：响应式指标卡片 */}\n                        <Col xs={24} sm={24} md={10} lg={12} xl={10}>\n                          <Row\n                            gutter={[4, 4]}\n                            justify={{ xs: 'start', md: 'end' }}\n                          >\n                            {/* 车辆资源 */}\n                            <Col xs={6} sm={6} md={6} lg={6} xl={6}>\n                              <div\n                                style={{\n                                  background: '#f0f7ff',\n                                  border: '1px solid #d9e8ff',\n                                  borderRadius: 6,\n                                  padding: '4px 6px',\n                                  textAlign: 'center',\n                                  minWidth: '45px',\n                                }}\n                              >\n                                <Flex vertical align=\"center\" gap={1}>\n                                  <CarOutlined\n                                    style={{ color: '#1890ff', fontSize: 12 }}\n                                  />\n                                  <Text\n                                    strong\n                                    style={{\n                                      fontSize: 14,\n                                      color: '#1890ff',\n                                      lineHeight: 1,\n                                    }}\n                                  >\n                                    {item.stats?.vehicles || 0}\n                                  </Text>\n                                  <Text style={{ fontSize: 8, color: '#666' }}>\n                                    车辆\n                                  </Text>\n                                </Flex>\n                              </div>\n                            </Col>\n\n                            {/* 人员资源 */}\n                            <Col xs={6} sm={6} md={6} lg={6} xl={6}>\n                              <div\n                                style={{\n                                  background: '#f6ffed',\n                                  border: '1px solid #d1f0be',\n                                  borderRadius: 6,\n                                  padding: '4px 6px',\n                                  textAlign: 'center',\n                                  minWidth: '45px',\n                                }}\n                              >\n                                <Flex vertical align=\"center\" gap={1}>\n                                  <UserOutlined\n                                    style={{ color: '#52c41a', fontSize: 12 }}\n                                  />\n                                  <Text\n                                    strong\n                                    style={{\n                                      fontSize: 14,\n                                      color: '#52c41a',\n                                      lineHeight: 1,\n                                    }}\n                                  >\n                                    {item.stats?.personnel || 0}\n                                  </Text>\n                                  <Text style={{ fontSize: 8, color: '#666' }}>\n                                    人员\n                                  </Text>\n                                </Flex>\n                              </div>\n                            </Col>\n\n                            {/* 临期事项 */}\n                            <Col xs={6} sm={6} md={6} lg={6} xl={6}>\n                              <div\n                                style={{\n                                  background: '#fff7e6',\n                                  border: '1px solid #ffd666',\n                                  borderRadius: 6,\n                                  padding: '4px 6px',\n                                  textAlign: 'center',\n                                  minWidth: '45px',\n                                }}\n                              >\n                                <Flex vertical align=\"center\" gap={1}>\n                                  <ExclamationCircleOutlined\n                                    style={{ color: '#faad14', fontSize: 12 }}\n                                  />\n                                  <Text\n                                    strong\n                                    style={{\n                                      fontSize: 14,\n                                      color: '#faad14',\n                                      lineHeight: 1,\n                                    }}\n                                  >\n                                    {item.stats?.expiring || 0}\n                                  </Text>\n                                  <Text style={{ fontSize: 8, color: '#666' }}>\n                                    临期\n                                  </Text>\n                                </Flex>\n                              </div>\n                            </Col>\n\n                            {/* 逾期事项 */}\n                            <Col xs={6} sm={6} md={6} lg={6} xl={6}>\n                              <div\n                                style={{\n                                  background: '#fff1f0',\n                                  border: '1px solid #ffccc7',\n                                  borderRadius: 6,\n                                  padding: '4px 6px',\n                                  textAlign: 'center',\n                                  minWidth: '45px',\n                                }}\n                              >\n                                <Flex vertical align=\"center\" gap={1}>\n                                  <ExclamationCircleOutlined\n                                    style={{ color: '#ff4d4f', fontSize: 12 }}\n                                  />\n                                  <Text\n                                    strong\n                                    style={{\n                                      fontSize: 14,\n                                      color: '#ff4d4f',\n                                      lineHeight: 1,\n                                    }}\n                                  >\n                                    {item.stats?.overdue || 0}\n                                  </Text>\n                                  <Text style={{ fontSize: 8, color: '#666' }}>\n                                    逾期\n                                  </Text>\n                                </Flex>\n                              </div>\n                            </Col>\n                          </Row>\n                        </Col>\n                      </Row>\n                    </ProCard>\n                )}\n              />\n            )}\n          </Spin>\n        )}\n      </ProCard>\n\n      {/* 创建团队功能已移至设置页面 */}\n    </>\n  );\n};\n\nexport default TeamListCard;\n", "import {\n  CalendarOutlined,\n  CheckOutlined,\n  DeleteOutlined,\n  EditOutlined,\n  MoreOutlined,\n  PlusOutlined,\n  SearchOutlined,\n} from '@ant-design/icons';\nimport {\n  Alert,\n  Button,\n  Col,\n  Dropdown,\n  Flex,\n  Form,\n  Input,\n  Modal,\n\n  Progress,\n  Row,\n  Select,\n  Space,\n  Spin,\n  Tabs,\n  Tooltip,\n  Typography,\n} from 'antd';\nimport { ModalForm, ProList, ProCard } from '@ant-design/pro-components';\nimport React, { useEffect, useState } from 'react';\nimport { TodoService } from '@/services/todo';\nimport type { TodoResponse, TodoStatsResponse } from '@/types/api';\n\nconst { Text } = Typography;\nconst { TabPane } = Tabs;\n\n// 使用API类型定义，不需要重复定义接口\ninterface TodoManagementProps {\n  onAddTodo?: (todo: TodoResponse) => void;\n  onUpdateTodo?: (id: number, updatedTodo: Partial<TodoResponse>) => void;\n  onDeleteTodo?: (id: number) => void;\n}\n\nconst TodoManagement: React.FC<TodoManagementProps> = () => {\n  // TODO数据状态管理\n  const [personalTasks, setPersonalTasks] = useState<TodoResponse[]>([]);\n  const [todoStats, setTodoStats] = useState<TodoStatsResponse>({\n    highPriorityCount: 0,\n    mediumPriorityCount: 0,\n    lowPriorityCount: 0,\n    totalCount: 0,\n    completedCount: 0,\n    completionPercentage: 0,\n  });\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState<string | null>(null);\n\n  // 待办事项状态管理\n  const [todoModalVisible, setTodoModalVisible] = useState(false);\n  const [todoForm] = Form.useForm();\n  const [editingTodoId, setEditingTodoId] = useState<number | null>(null);\n\n  // 过滤器状态\n  const [activeTab, setActiveTab] = useState<'all' | 'pending' | 'completed'>(\n    'pending',\n  );\n  const [searchText, setSearchText] = useState('');\n\n  // 获取TODO数据\n  useEffect(() => {\n    const fetchTodoData = async () => {\n      try {\n        setLoading(true);\n        setError(null);\n\n        console.log('TodoManagement: 开始获取TODO数据');\n\n        // 分别获取TODO列表和统计数据，避免一个失败影响另一个\n        const todosPromise = TodoService.getUserTodos().catch((error) => {\n          console.error('获取TODO列表失败:', error);\n          return [];\n        });\n\n        const statsPromise = TodoService.getTodoStats().catch((error) => {\n          console.error('获取TODO统计失败:', error);\n          return {\n            highPriorityCount: 0,\n            mediumPriorityCount: 0,\n            lowPriorityCount: 0,\n            totalCount: 0,\n            completedCount: 0,\n            completionPercentage: 0,\n          };\n        });\n\n        const [todos, stats] = await Promise.all([todosPromise, statsPromise]);\n\n        console.log('TodoManagement: 获取到TODO列表:', todos);\n        console.log('TodoManagement: 获取到统计数据:', stats);\n\n        setPersonalTasks(todos);\n        setTodoStats(stats);\n      } catch (error) {\n        console.error('获取TODO数据时发生未知错误:', error);\n        setError('获取TODO数据失败，请刷新页面重试');\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    fetchTodoData();\n  }, []);\n\n  // 根据激活的标签和搜索文本过滤任务\n  const filteredPersonalTasks = (personalTasks || []).filter((task) => {\n    // 根据标签过滤\n    if (activeTab === 'pending' && task.status === 1) return false;\n    if (activeTab === 'completed' && task.status === 0) return false;\n\n    // 根据搜索文本过滤\n    if (\n      searchText &&\n      !task.title.toLowerCase().includes(searchText.toLowerCase())\n    ) {\n      return false;\n    }\n\n    return true;\n  });\n\n  // 处理待办事项操作\n  const handleToggleTodoStatus = async (id: number) => {\n    try {\n      const task = personalTasks.find((t) => t.id === id);\n      if (!task) {\n        return;\n      }\n\n      const newStatus = task.status === 0 ? 1 : 0;\n\n      await TodoService.updateTodo(id, { status: newStatus });\n\n      // 更新本地状态\n      setPersonalTasks(\n        personalTasks.map((task) =>\n          task.id === id ? { ...task, status: newStatus } : task,\n        ),\n      );\n\n      // 刷新统计数据\n      try {\n        const stats = await TodoService.getTodoStats();\n        setTodoStats(stats);\n      } catch (statsError) {\n        // 统计数据刷新失败不影响主要操作\n      }\n    } catch (error) {\n      // 错误处理由响应拦截器统一处理\n    }\n  };\n\n  const handleAddOrUpdateTodo = async (values: any) => {\n    try {\n      if (editingTodoId) {\n        // 更新现有待办事项\n        const updatedTodo = await TodoService.updateTodo(editingTodoId, {\n          title: values.name,\n          priority: values.priority,\n        });\n\n        setPersonalTasks(\n          personalTasks.map((task) =>\n            task.id === editingTodoId ? updatedTodo : task,\n          ),\n        );\n      } else {\n        // 添加新待办事项\n        const newTodo = await TodoService.createTodo({\n          title: values.name,\n          priority: values.priority,\n        });\n\n        setPersonalTasks([newTodo, ...personalTasks]);\n      }\n\n      // 刷新统计数据\n      try {\n        const stats = await TodoService.getTodoStats();\n        setTodoStats(stats);\n      } catch (statsError) {\n        // 统计数据刷新失败不影响主要操作\n      }\n\n      // 重置表单并关闭模态框\n      setTodoModalVisible(false);\n      setEditingTodoId(null);\n      todoForm.resetFields();\n    } catch (error) {\n      // 错误处理由响应拦截器统一处理\n    }\n  };\n\n  const handleDeleteTodo = async (id: number) => {\n    try {\n      await TodoService.deleteTodo(id);\n      setPersonalTasks(personalTasks.filter((task) => task.id !== id));\n\n      // 刷新统计数据\n      try {\n        const stats = await TodoService.getTodoStats();\n        setTodoStats(stats);\n      } catch (statsError) {\n        // 统计数据刷新失败不影响主要操作\n      }\n    } catch (error) {\n      // 错误处理由响应拦截器统一处理\n    }\n  };\n\n  return (\n    <ProCard\n      title=\"待办事项/任务列表\"\n      style={{\n        borderRadius: 8,\n        height: 'fit-content',\n        minHeight: '600px', // 确保右列有足够的高度\n      }}\n      headStyle={{\n        borderBottom: '1px solid #f0f0f0',\n        paddingBottom: 12,\n      }}\n      bodyStyle={{\n        padding: '16px',\n      }}\n    >\n      {/* 响应式标题行：搜索框、新增按钮、优先级计数和完成率 */}\n      <div\n        style={{\n          marginBottom: 16,\n          padding: '12px 16px',\n          background: '#fafbfc',\n          borderRadius: 8,\n          border: '1px solid #f0f0f0',\n        }}\n      >\n        {/* 使用 Row/Col 实现三列响应式布局 */}\n        <Row gutter={[16, 12]} align=\"middle\">\n          {/* 第一列：搜索框和新增按钮 */}\n          <Col xs={24} sm={24} md={8} lg={8} xl={8}>\n            <Flex align=\"center\" gap={12} style={{ width: '100%' }}>\n              <Input.Search\n                placeholder=\"搜索任务...\"\n                allowClear\n                prefix={<SearchOutlined />}\n                value={searchText}\n                onChange={(e) => setSearchText(e.target.value)}\n                style={{ flex: 1 }}\n                size=\"middle\"\n              />\n\n              <Button\n                type=\"primary\"\n                icon={<PlusOutlined />}\n                onClick={() => {\n                  setEditingTodoId(null);\n                  todoForm.resetFields();\n                  setTodoModalVisible(true);\n                }}\n                style={{\n                  background: '#1890ff',\n                  borderColor: '#1890ff',\n                  boxShadow: '0 2px 4px rgba(24, 144, 255, 0.3)',\n                  fontWeight: 500,\n                  minWidth: 80,\n                }}\n                size=\"middle\"\n              >\n                新增\n              </Button>\n            </Flex>\n          </Col>\n\n          {/* 第二列：完成率和优先级计数卡片 */}\n          <Col xs={24} sm={24} md={16} lg={16} xl={16}>\n            <Flex align=\"center\" justify=\"center\" wrap=\"wrap\" gap={12}>\n              {/* 完成率卡片 */}\n              <div\n                style={{\n                  background: '#f6ffed',\n                  border: '1px solid #b7eb8f',\n                  borderRadius: 8,\n                  padding: '8px 12px',\n                  display: 'flex',\n                  alignItems: 'center',\n                  gap: 8,\n                  minWidth: 120,\n                  boxShadow: '0 1px 3px rgba(0,0,0,0.1)',\n                }}\n              >\n                <Tooltip\n                  title={`完成率: ${todoStats.completionPercentage}% (${todoStats.completedCount}/${todoStats.totalCount})`}\n                >\n                  <Flex align=\"center\" gap={6}>\n                    <Text\n                      style={{ fontSize: 12, fontWeight: 500, color: '#389e0d' }}\n                    >\n                      完成率\n                    </Text>\n                    <Progress\n                      percent={todoStats.completionPercentage}\n                      size=\"small\"\n                      style={{ width: 60 }}\n                      strokeColor=\"#52c41a\"\n                      showInfo={false}\n                    />\n                    <Text\n                      style={{ fontSize: 12, fontWeight: 600, color: '#389e0d' }}\n                    >\n                      {todoStats.completionPercentage}%\n                    </Text>\n                  </Flex>\n                </Tooltip>\n              </div>\n\n              {/* 高优先级卡片 */}\n              <div\n                style={{\n                  background: '#fff2f0',\n                  border: '1px solid #ffccc7',\n                  borderRadius: 8,\n                  padding: '8px 12px',\n                  display: 'flex',\n                  alignItems: 'center',\n                  gap: 6,\n                  minWidth: 80,\n                  boxShadow: '0 1px 3px rgba(0,0,0,0.1)',\n                }}\n              >\n                <Tooltip title={`高优先级任务: ${todoStats.highPriorityCount}个`}>\n                  <Flex align=\"center\" gap={6}>\n                    <div\n                      style={{\n                        width: 8,\n                        height: 8,\n                        borderRadius: '50%',\n                        background: '#ff4d4f',\n                      }}\n                    />\n                    <Text\n                      style={{\n                        fontSize: 12,\n                        fontWeight: 500,\n                        color: '#cf1322',\n                      }}\n                    >\n                      高\n                    </Text>\n                    <Text\n                      style={{\n                        fontSize: 14,\n                        fontWeight: 600,\n                        color: '#cf1322',\n                      }}\n                    >\n                      {todoStats.highPriorityCount}\n                    </Text>\n                  </Flex>\n                </Tooltip>\n              </div>\n\n              {/* 中优先级卡片 */}\n              <div\n                style={{\n                  background: '#fffbe6',\n                  border: '1px solid #ffe58f',\n                  borderRadius: 8,\n                  padding: '8px 12px',\n                  display: 'flex',\n                  alignItems: 'center',\n                  gap: 6,\n                  minWidth: 80,\n                  boxShadow: '0 1px 3px rgba(0,0,0,0.1)',\n                }}\n              >\n                <Tooltip title={`中优先级任务: ${todoStats.mediumPriorityCount}个`}>\n                  <Flex align=\"center\" gap={6}>\n                    <div\n                      style={{\n                        width: 8,\n                        height: 8,\n                        borderRadius: '50%',\n                        background: '#faad14',\n                      }}\n                    />\n                    <Text\n                      style={{\n                        fontSize: 12,\n                        fontWeight: 500,\n                        color: '#d48806',\n                      }}\n                    >\n                      中\n                    </Text>\n                    <Text\n                      style={{\n                        fontSize: 14,\n                        fontWeight: 600,\n                        color: '#d48806',\n                      }}\n                    >\n                      {todoStats.mediumPriorityCount}\n                    </Text>\n                  </Flex>\n                </Tooltip>\n              </div>\n\n              {/* 低优先级卡片 */}\n              <div\n                style={{\n                  background: '#fafafa',\n                  border: '1px solid #d9d9d9',\n                  borderRadius: 8,\n                  padding: '8px 12px',\n                  display: 'flex',\n                  alignItems: 'center',\n                  gap: 6,\n                  minWidth: 80,\n                  boxShadow: '0 1px 3px rgba(0,0,0,0.1)',\n                }}\n              >\n                <Tooltip title={`低优先级任务: ${todoStats.lowPriorityCount}个`}>\n                  <Flex align=\"center\" gap={6}>\n                    <div\n                      style={{\n                        width: 8,\n                        height: 8,\n                        borderRadius: '50%',\n                        background: '#8c8c8c',\n                      }}\n                    />\n                    <Text\n                      style={{\n                        fontSize: 12,\n                        fontWeight: 500,\n                        color: '#595959',\n                      }}\n                    >\n                      低\n                    </Text>\n                    <Text\n                      style={{\n                        fontSize: 14,\n                        fontWeight: 600,\n                        color: '#595959',\n                      }}\n                    >\n                      {todoStats.lowPriorityCount}\n                    </Text>\n                  </Flex>\n                </Tooltip>\n              </div>\n            </Flex>\n          </Col>\n        </Row>\n      </div>\n\n      {/* 第二行：标签页 */}\n      <Tabs\n        activeKey={activeTab}\n        onChange={(key) => setActiveTab(key as 'all' | 'pending' | 'completed')}\n        size=\"middle\"\n        style={{ marginBottom: 8 }}\n      >\n        <TabPane tab=\"全部\" key=\"all\" />\n        <TabPane tab=\"待处理\" key=\"pending\" />\n        <TabPane tab=\"已完成\" key=\"completed\" />\n      </Tabs>\n\n      {/* 待办事项列表 */}\n      {error ? (\n        <Alert\n          message=\"TODO数据加载失败\"\n          description={error}\n          type=\"error\"\n          showIcon\n          style={{ marginBottom: 16 }}\n        />\n      ) : (\n        <Spin spinning={loading}>\n          <ProList\n            dataSource={filteredPersonalTasks}\n            renderItem={(item) => {\n              return (\n                <div\n                  className=\"todo-item\"\n                  style={{\n                    padding: '10px 16px',\n                    marginBottom: 12,\n                    borderRadius: 8,\n                    background: '#fff',\n                    opacity: item.status === 1 ? 0.7 : 1,\n                    borderLeft: `3px solid ${\n                      item.status === 1\n                        ? '#52c41a'\n                        : item.priority === 3\n                          ? '#ff4d4f'\n                          : item.priority === 2\n                            ? '#faad14'\n                            : '#8c8c8c'\n                    }`,\n                    boxShadow: '0 1px 4px rgba(0,0,0,0.05)',\n                  }}\n                >\n                  <Flex align=\"center\" gap={12} style={{ width: '100%' }}>\n                    {/* 左侧状态和优先级指示器 */}\n                    <Flex vertical align=\"center\">\n                      {item.status === 1 ? (\n                        <Flex\n                          align=\"center\"\n                          justify=\"center\"\n                          style={{\n                            width: 22,\n                            height: 22,\n                            borderRadius: '50%',\n                            background: '#52c41a',\n                          }}\n                        >\n                          <CheckOutlined\n                            style={{ color: '#fff', fontSize: 12 }}\n                          />\n                        </Flex>\n                      ) : (\n                        <div\n                          style={{\n                            width: 18,\n                            height: 18,\n                            borderRadius: '50%',\n                            border: `2px solid ${\n                              item.priority === 3\n                                ? '#ff4d4f'\n                                : item.priority === 2\n                                  ? '#faad14'\n                                  : '#8c8c8c'\n                            }`,\n                          }}\n                        />\n                      )}\n\n                      <div\n                        style={{\n                          width: 2,\n                          height: 24,\n                          background: '#f0f0f0',\n                          marginTop: 4,\n                        }}\n                      />\n                    </Flex>\n\n                    {/* 任务信息区 */}\n                    <Flex vertical style={{ flex: 1 }}>\n                      <Text\n                        style={{\n                          fontSize: 14,\n                          fontWeight: item.priority === 3 ? 500 : 'normal',\n                          textDecoration:\n                            item.status === 1 ? 'line-through' : 'none',\n                          color: item.status === 1 ? '#8c8c8c' : '#262626',\n                        }}\n                      >\n                        {item.title}\n                      </Text>\n\n                      {/* 显示创建日期 */}\n                      <Space align=\"center\" size={6} style={{ marginTop: 4 }}>\n                        <CalendarOutlined\n                          style={{\n                            fontSize: 12,\n                            color: '#8c8c8c',\n                          }}\n                        />\n                        <Text type=\"secondary\" style={{ fontSize: 12 }}>\n                          创建于:{' '}\n                          {new Date(item.createdAt).toLocaleDateString('zh-CN')}\n                        </Text>\n                      </Space>\n                    </Flex>\n\n                    {/* 操作按钮区 */}\n                    <Dropdown\n                      trigger={['click']}\n                      menu={{\n                        items: [\n                          {\n                            key: 'complete',\n                            label:\n                              item.status === 1 ? '标记未完成' : '标记完成',\n                            icon: (\n                              <CheckOutlined\n                                style={{\n                                  color:\n                                    item.status === 1 ? '#8c8c8c' : '#52c41a',\n                                  fontSize: 14,\n                                }}\n                              />\n                            ),\n                          },\n                          {\n                            key: 'edit',\n                            label: '编辑任务',\n                            icon: <EditOutlined style={{ color: '#8c8c8c' }} />,\n                          },\n                          {\n                            key: 'delete',\n                            label: '删除任务',\n                            icon: (\n                              <DeleteOutlined style={{ color: '#ff4d4f' }} />\n                            ),\n                            danger: true,\n                          },\n                        ],\n                        onClick: ({ key }) => {\n                          if (key === 'complete') {\n                            handleToggleTodoStatus(item.id);\n                          } else if (key === 'edit') {\n                            setEditingTodoId(item.id);\n                            todoForm.setFieldsValue({\n                              name: item.title,\n                              priority: item.priority,\n                            });\n                            setTodoModalVisible(true);\n                          } else if (key === 'delete') {\n                            handleDeleteTodo(item.id);\n                          }\n                        },\n                      }}\n                    >\n                      <Button\n                        type=\"text\"\n                        size=\"small\"\n                        icon={<MoreOutlined />}\n                        style={{ width: 32, height: 32 }}\n                      />\n                    </Dropdown>\n                  </Flex>\n                </div>\n              );\n            }}\n          />\n\n          {/* 待办事项表单模态框 */}\n          <ModalForm\n            title={editingTodoId ? '编辑待办事项' : '新增待办事项'}\n            open={todoModalVisible}\n            onOpenChange={(visible) => {\n              if (!visible) {\n                setTodoModalVisible(false);\n                todoForm.resetFields();\n              }\n            }}\n            form={todoForm}\n            layout=\"vertical\"\n            onFinish={handleAddOrUpdateTodo}\n            autoComplete=\"off\"\n            width={500}\n            modalProps={{\n              centered: true,\n              destroyOnClose: true,\n              maskClosable: false,\n              keyboard: false,\n            }}\n            submitter={{\n              searchConfig: {\n                submitText: editingTodoId ? '更新任务' : '创建任务',\n                resetText: '取消',\n              },\n              submitButtonProps: {\n                style: {\n                  background: '#1890ff',\n                  borderColor: '#1890ff',\n                  boxShadow: '0 2px 4px rgba(24, 144, 255, 0.3)',\n                },\n                icon: editingTodoId ? <EditOutlined /> : <PlusOutlined />,\n              },\n              resetButtonProps: {\n                style: {\n                  borderColor: '#d9d9d9',\n                },\n              },\n              onReset: () => {\n                setTodoModalVisible(false);\n                todoForm.resetFields();\n              },\n            }}\n            preserve={false}\n          >\n            <Form.Item\n              name=\"name\"\n              label=\"任务名称\"\n              rules={[{ required: true, message: '请输入任务名称' }]}\n            >\n              <Input\n                placeholder=\"请输入任务名称\"\n                size=\"large\"\n                style={{ borderRadius: 6 }}\n              />\n            </Form.Item>\n\n            <Form.Item\n              name=\"priority\"\n              label=\"优先级\"\n              initialValue={2}\n              rules={[{ required: true, message: '请选择优先级' }]}\n            >\n              <Select\n                size=\"large\"\n                options={[\n                  { value: 3, label: '高优先级' },\n                  { value: 2, label: '中优先级' },\n                  { value: 1, label: '低优先级' },\n                ]}\n                style={{ borderRadius: 6 }}\n              />\n            </Form.Item>\n          </ModalForm>\n        </Spin>\n      )}\n    </ProCard>\n  );\n};\n\nexport default TodoManagement;\n", "import { SettingOutlined, TeamOutlined, UserOutlined } from '@ant-design/icons';\nimport {\n  Avatar,\n  Form,\n  Input,\n  Modal,\n  Space,\n  Tabs,\n  Typography,\n  message,\n} from 'antd';\nimport React, { useEffect, useState } from 'react';\nimport type { UserProfileDetailResponse } from '@/types/user';\n\nconst { Title, Text } = Typography;\n\ninterface UnifiedSettingsModalProps {\n  visible: boolean;\n  onCancel: () => void;\n  onSuccess?: () => void;\n  userInfo: UserProfileDetailResponse;\n}\n\n/**\n * 统一设置Modal组件\n *\n * 提供个人信息修改和新建团队功能的统一界面。\n * 使用Tab页面结构组织不同的设置功能。\n *\n * 主要功能：\n * 1. 个人信息修改Tab：编辑用户基本信息\n * 2. 新建团队Tab：创建新团队\n *\n * Props:\n * - visible: 控制Modal显示/隐藏\n * - onCancel: 取消操作回调\n * - onSuccess: 操作成功回调\n * - userInfo: 当前用户信息\n */\nconst UnifiedSettingsModal: React.FC<UnifiedSettingsModalProps> = ({\n  visible,\n  onCancel,\n  onSuccess,\n  userInfo,\n}) => {\n  const [personalForm] = Form.useForm();\n  const [teamForm] = Form.useForm();\n  const [activeTab, setActiveTab] = useState('personal');\n\n  // 当Modal打开时，填充个人信息表单数据\n  useEffect(() => {\n    if (visible && userInfo) {\n      personalForm.setFieldsValue({\n        name: userInfo.name,\n        email: userInfo.email,\n        telephone: userInfo.telephone,\n        position: userInfo.position,\n      });\n    }\n  }, [visible, userInfo, personalForm]);\n\n  // 处理个人信息提交\n  const handlePersonalSubmit = async () => {\n    try {\n      const values = await personalForm.validateFields();\n      console.log('更新个人信息:', values);\n      \n      // TODO: 调用更新用户信息的API\n      // await UserService.updateUserProfile(values);\n      \n      message.success('个人信息更新成功！');\n      onSuccess?.();\n      onCancel();\n    } catch (error) {\n      console.error('更新个人信息失败:', error);\n      message.error('更新个人信息失败，请稍后重试');\n    }\n  };\n\n  // 处理团队创建提交\n  const handleTeamSubmit = async () => {\n    try {\n      const values = await teamForm.validateFields();\n      console.log('创建团队:', values);\n      \n      // TODO: 调用创建团队的API\n      // await TeamService.createTeam(values);\n      \n      message.success('团队创建成功！');\n      teamForm.resetFields();\n      onSuccess?.();\n      onCancel();\n    } catch (error) {\n      console.error('创建团队失败:', error);\n      message.error('创建团队失败，请稍后重试');\n    }\n  };\n\n  // 处理取消操作\n  const handleCancel = () => {\n    personalForm.resetFields();\n    teamForm.resetFields();\n    setActiveTab('personal');\n    onCancel();\n  };\n\n  // 根据当前Tab决定提交操作\n  const handleOk = () => {\n    if (activeTab === 'personal') {\n      handlePersonalSubmit();\n    } else {\n      handleTeamSubmit();\n    }\n  };\n\n  return (\n    <Modal\n      title={\n        <Space align=\"center\">\n          <SettingOutlined style={{ fontSize: 18, color: '#1890ff' }} />\n          <Title level={4} style={{ margin: 0, fontSize: 16 }}>\n            设置\n          </Title>\n        </Space>\n      }\n      open={visible}\n      onOk={handleOk}\n      onCancel={handleCancel}\n      okText={activeTab === 'personal' ? '保存设置' : '创建团队'}\n      cancelText=\"取消\"\n \n      destroyOnClose\n   \n    >\n      <Tabs\n        activeKey={activeTab}\n        onChange={setActiveTab}\n        style={{ marginTop: -8 }}\n        items={[\n          {\n            key: 'personal',\n            label: (\n              <Space align=\"center\">\n                <UserOutlined />\n                <span>个人信息</span>\n              </Space>\n            ),\n            children: (\n              <div>\n                <div style={{ marginBottom: 16 }}>\n                  <Text style={{ color: '#8c8c8c', fontSize: 14 }}>\n                    编辑您的个人信息和偏好设置\n                  </Text>\n                </div>\n\n                <Form\n                  form={personalForm}\n                  layout=\"vertical\"\n                  requiredMark={false}\n                  autoComplete=\"off\"\n                >\n                  {/* 姓名 */}\n                  <Form.Item\n                    label={\n                      <Text style={{ fontWeight: 600, fontSize: 14 }}>\n                        姓名\n                      </Text>\n                    }\n                    name=\"name\"\n                    rules={[\n                      { required: true, message: '请输入姓名' },\n                      { min: 2, message: '姓名至少2个字符' },\n                      { max: 20, message: '姓名不能超过20个字符' },\n                    ]}\n                  >\n                    <Input\n                      placeholder=\"请输入姓名\"\n                      style={{\n                        borderRadius: 6,\n                        fontSize: 14,\n                      }}\n                    />\n                  </Form.Item>\n\n                  {/* 职位 */}\n                  <Form.Item\n                    label={\n                      <Text style={{ fontWeight: 600, fontSize: 14 }}>\n                        职位\n                      </Text>\n                    }\n                    name=\"position\"\n                    rules={[\n                      { max: 50, message: '职位不能超过50个字符' },\n                    ]}\n                  >\n                    <Input\n                      placeholder=\"请输入职位（可选）\"\n                      style={{\n                        borderRadius: 6,\n                        fontSize: 14,\n                      }}\n                    />\n                  </Form.Item>\n\n                  {/* 邮箱 */}\n                  <Form.Item\n                    label={\n                      <Text style={{ fontWeight: 600, fontSize: 14 }}>\n                        邮箱\n                      </Text>\n                    }\n                    name=\"email\"\n                    rules={[\n                      { required: true, message: '请输入邮箱' },\n                      { type: 'email', message: '请输入有效的邮箱地址' },\n                    ]}\n                  >\n                    <Input\n                      placeholder=\"请输入邮箱\"\n                      style={{\n                        borderRadius: 6,\n                        fontSize: 14,\n                      }}\n                    />\n                  </Form.Item>\n\n                  {/* 电话 */}\n                  <Form.Item\n                    label={\n                      <Text style={{ fontWeight: 600, fontSize: 14 }}>\n                        电话\n                      </Text>\n                    }\n                    name=\"telephone\"\n                    rules={[\n                      { pattern: /^1[3-9]\\d{9}$/, message: '请输入有效的手机号码' },\n                    ]}\n                  >\n                    <Input\n                      placeholder=\"请输入电话（可选）\"\n                      style={{\n                        borderRadius: 6,\n                        fontSize: 14,\n                      }}\n                    />\n                  </Form.Item>\n                </Form>\n              </div>\n            ),\n          },\n          {\n            key: 'team',\n            label: (\n              <Space align=\"center\">\n                <TeamOutlined />\n                <span>新建团队</span>\n              </Space>\n            ),\n            children: (\n              <div>\n                <div style={{ marginBottom: 24, textAlign: 'center' }}>\n                  <Text style={{ color: '#8c8c8c', fontSize: 14, lineHeight: 1.6 }}>\n                    创建一个新的团队来协作管理项目和任务\n                  </Text>\n                </div>\n\n                <Form\n                  form={teamForm}\n                  layout=\"vertical\"\n                  requiredMark={false}\n                  autoComplete=\"off\"\n                >\n                  {/* 团队名称 */}\n                  <Form.Item\n                    label={\n                      <Text style={{ fontWeight: 600, fontSize: 15 }}>\n                        团队名称\n                      </Text>\n                    }\n                    name=\"teamName\"\n                    rules={[\n                      { required: true, message: '请输入团队名称' },\n                      { min: 2, message: '团队名称至少2个字符' },\n                      { max: 50, message: '团队名称不能超过50个字符' },\n                    ]}\n                    style={{ marginBottom: 0 }}\n                  >\n                    <Input\n                      placeholder=\"请输入团队名称\"\n                      size=\"large\"\n                      style={{\n                        borderRadius: 8,\n                        fontSize: 15,\n                        padding: '12px 16px',\n                        border: '2px solid #d9d9d9',\n                        transition: 'all 0.3s ease',\n                      }}\n                      onFocus={(e) => {\n                        e.target.style.borderColor = '#1890ff';\n                        e.target.style.boxShadow = '0 0 0 2px rgba(24, 144, 255, 0.1)';\n                      }}\n                      onBlur={(e) => {\n                        e.target.style.borderColor = '#d9d9d9';\n                        e.target.style.boxShadow = 'none';\n                      }}\n                    />\n                  </Form.Item>\n                </Form>\n              </div>\n            ),\n          },\n        ]}\n      />\n    </Modal>\n  );\n};\n\nexport default UnifiedSettingsModal;\n", "import { useModel } from '@umijs/max';\nimport { Col, Row, Spin } from 'antd';\nimport { ProCard } from '@ant-design/pro-components';\nimport React from 'react';\nimport UserFloatButton from '@/components/FloatButton';\nimport TeamListCard from './TeamListCard';\nimport TodoManagement from './TodoManagement';\nimport PersonalInfo from './PersonalInfo';\nimport DataOverview from './DataOverview';\n\n/**\n * 个人中心页面组件\n *\n * 这是用户的个人中心主页面，提供用户信息管理、团队管理、待办事项等功能。\n * 是用户进行个人设置和团队操作的主要入口页面。\n *\n * 页面功能：\n * 1. 用户个人信息展示和编辑\n * 2. 团队列表显示和团队切换\n * 3. 个人待办事项管理\n * 4. 全局浮动操作按钮\n *\n * 页面结构：\n * - 左列：个人信息、数据概览、上次登录信息、团队列表（响应式布局）\n * - 右列：待办事项管理（响应式布局）\n * - 浮动：全局操作按钮\n *\n * 权限控制：\n * - 需要用户登录才能访问\n * - 自动检查登录状态并重定向\n * - 支持登录状态变化的实时响应\n *\n * 响应式设计：\n * - 移动端：垂直堆叠布局\n * - 桌面端：左右分栏布局\n * - 自适应不同屏幕尺寸\n */\nconst PersonalCenterPage: React.FC = () => {\n  /**\n   * 全局状态管理\n   *\n   * 从UmiJS全局状态中获取用户信息和加载状态：\n   * - initialState: 包含用户和团队信息的全局状态\n   * - loading: 全局状态的加载状态\n   */\n  const { initialState, loading } = useModel('@@initialState');\n\n\n\n  /**\n   * 加载状态处理\n   *\n   * 当全局状态正在初始化时，显示加载界面。\n   * 这确保了用户在状态加载完成前看到友好的加载提示。\n   */\n  if (loading) {\n    return (\n      <div\n        style={{\n          minHeight: '100vh',\n          background: '#f5f8ff',\n          display: 'flex',\n          justifyContent: 'center',\n          alignItems: 'center',\n        }}\n      >\n        <Spin size=\"large\" />\n        <div style={{ marginLeft: 16 }}>正在加载用户信息...</div>\n      </div>\n    );\n  }\n\n  /**\n   * 登录状态检查已由应用级路由守卫处理\n   *\n   * 移除页面级别的登录检查，避免与应用级路由守卫冲突。\n   * 应用级路由守卫在 app.tsx 中的 onPageChange 函数已经处理了\n   * 用户登录状态检查和重定向逻辑，无需在页面组件中重复检查。\n   *\n   * 这样可以避免登录成功后的状态更新时序问题，确保用户\n   * 一次登录成功后能够正常访问个人中心页面。\n   */\n\n  return (\n    <>\n      {/* 页面主容器 */}\n      <div\n        style={{\n          minHeight: '100vh',\n          background: '#f5f8ff', // 浅蓝色背景，营造清新的视觉效果\n          padding: '12px 12px 24px 12px', // 移动端优化：减少左右边距，增加底部边距\n        }}\n      >\n        {/*\n         * 主内容卡片容器\n         *\n         * 使用Card组件作为主要内容的容器，提供：\n         * 1. 统一的视觉边界和阴影效果\n         * 2. 响应式的内边距设置\n         * 3. 圆角设计提升视觉体验\n         * 4. 全高度布局适配不同屏幕\n         */}\n        <ProCard\n          style={{\n            width: '100%',\n            minHeight: 'calc(100vh - 48px)', // 减去外层padding的高度\n            borderRadius: '12px', // 圆角设计\n            boxShadow: '0 4px 12px rgba(0, 0, 0, 0.1)', // 轻微阴影效果\n          }}\n          bodyStyle={{\n            padding: '24px', // 内容区域的内边距\n          }}\n        >\n          {/*\n           * 响应式两列布局\n           *\n           * 使用Ant Design的Row/Col组件实现响应式两列布局：\n           * - 移动端：垂直堆叠，所有组件占满宽度\n           * - 桌面端：左列包含个人信息、数据概览、登录信息、团队列表；右列包含待办事项\n           * - gutter: 组件间距设置\n           * - margin: 0: 避免Row组件的默认负边距影响布局\n           */}\n          <Row gutter={[24, 16]} style={{ margin: 0 }}>\n            {/*\n             * 左列：个人信息区域\n             *\n             * 包含以下组件（按顺序）：\n             * 1. 个人信息部分（用户详情、头像、姓名、联系方式、登录信息等）\n             * 2. 数据概览部分（统计信息、指标或摘要数据）\n             * 3. 团队列表部分（用户所属团队列表）\n             *\n             * 响应式布局：\n             * - 移动端(xs-md)：全宽显示\n             * - 桌面端(lg+)：占据左半部分\n             */}\n            <Col\n              xs={24}\n              sm={24}\n              md={24}\n              lg={12}\n              xl={12}\n              xxl={12}\n            >\n              {/* 个人信息部分（包含用户详情、头像、姓名、联系方式、登录信息等） */}\n              <PersonalInfo />\n\n              {/* 数据概览部分（统计信息、指标或摘要数据） */}\n              <DataOverview />\n\n              {/* 团队列表部分（用户所属团队列表） */}\n              <TeamListCard />\n            </Col>\n\n            {/*\n             * 右列：待办事项管理区域\n             *\n             * 个人待办事项的管理界面，支持添加、编辑、删除待办事项。\n             * 响应式布局：\n             * - 移动端(xs-md)：全宽显示\n             * - 桌面端(lg+)：占据右半部分\n             */}\n            <Col\n              xs={24}\n              sm={24}\n              md={24}\n              lg={12}\n              xl={12}\n              xxl={12}\n            >\n              <TodoManagement />\n            </Col>\n          </Row>\n        </ProCard>\n      </div>\n\n      {/*\n       * 全局浮动操作按钮\n       *\n       * 提供快速访问常用功能的浮动按钮，如：\n       * - 快速创建团队\n       * - 用户设置\n       * - 帮助信息\n       *\n       * 位置固定在页面右下角，不受页面滚动影响。\n       */}\n      <UserFloatButton />\n\n\n\n\n    </>\n  );\n};\n\nexport default PersonalCenterPage;\n", "/**\n * TODO服务\n */\n\nimport type {\n  CreateTodoRequest,\n  TodoResponse,\n  TodoStatsResponse,\n  UpdateTodoRequest,\n} from '@/types/api';\nimport { apiRequest } from '@/utils/request';\n\nexport class TodoService {\n  /**\n   * 获取用户的TODO列表\n   */\n  static async getUserTodos(): Promise<TodoResponse[]> {\n    const response = await apiRequest.get<TodoResponse[]>('/todos');\n    return response.data;\n  }\n\n  /**\n   * 创建TODO\n   */\n  static async createTodo(request: CreateTodoRequest): Promise<TodoResponse> {\n    const response = await apiRequest.post<TodoResponse>('/todos', request);\n    return response.data;\n  }\n\n  /**\n   * 更新TODO\n   */\n  static async updateTodo(\n    id: number,\n    request: UpdateTodoRequest,\n  ): Promise<TodoResponse> {\n    const response = await apiRequest.put<TodoResponse>(\n      `/todos/${id}`,\n      request,\n    );\n    return response.data;\n  }\n\n  /**\n   * 删除TODO\n   */\n  static async deleteTodo(id: number): Promise<void> {\n    await apiRequest.delete(`/todos/${id}`);\n  }\n\n  /**\n   * 获取TODO统计信息\n   */\n  static async getTodoStats(): Promise<TodoStatsResponse> {\n    const response = await apiRequest.get<TodoStatsResponse>('/todos/stats');\n    return response.data;\n  }\n}\n", "/**\n * 团队选择状态管理工具函数\n * 用于跟踪用户是否已经主动选择过团队，以区分初始登录状态和主动选择状态\n */\n\n// 团队选择历史的本地存储键\nconst TEAM_SELECTION_KEY = 'user_team_selection_history';\n\n/**\n * 获取用户的团队选择历史\n * @param userId 用户ID\n * @returns 用户选择过的团队ID集合\n */\nexport const getUserTeamSelectionHistory = (userId: number): Set<number> => {\n  try {\n    const history = localStorage.getItem(`${TEAM_SELECTION_KEY}_${userId}`);\n    if (history) {\n      return new Set(JSON.parse(history));\n    }\n  } catch (error) {\n    console.error('获取团队选择历史失败:', error);\n  }\n  return new Set();\n};\n\n/**\n * 记录用户选择了某个团队\n * @param userId 用户ID\n * @param teamId 团队ID\n */\nexport const recordTeamSelection = (userId: number, teamId: number): void => {\n  try {\n    const history = getUserTeamSelectionHistory(userId);\n    history.add(teamId);\n    localStorage.setItem(`${TEAM_SELECTION_KEY}_${userId}`, JSON.stringify([...history]));\n    console.log(`记录团队选择: 用户${userId}选择了团队${teamId}`);\n  } catch (error) {\n    console.error('记录团队选择历史失败:', error);\n  }\n};\n\n/**\n * 检查用户是否曾经选择过某个团队\n * @param userId 用户ID\n * @param teamId 团队ID\n * @returns 是否曾经选择过该团队\n */\nexport const hasUserSelectedTeam = (userId: number, teamId: number): boolean => {\n  const history = getUserTeamSelectionHistory(userId);\n  return history.has(teamId);\n};\n\n/**\n * 清除用户的团队选择历史（用于注销等场景）\n * @param userId 用户ID\n */\nexport const clearUserTeamSelectionHistory = (userId: number): void => {\n  try {\n    localStorage.removeItem(`${TEAM_SELECTION_KEY}_${userId}`);\n    console.log(`清除用户${userId}的团队选择历史`);\n  } catch (error) {\n    console.error('清除团队选择历史失败:', error);\n  }\n};\n\n/**\n * 获取所有用户的团队选择历史键（用于调试）\n * @returns 所有相关的localStorage键\n */\nexport const getAllTeamSelectionKeys = (): string[] => {\n  const keys: string[] = [];\n  for (let i = 0; i < localStorage.length; i++) {\n    const key = localStorage.key(i);\n    if (key && key.startsWith(TEAM_SELECTION_KEY)) {\n      keys.push(key);\n    }\n  }\n  return keys;\n};\n"], "names": [], "mappings": ";;;;;;;4BAyNA;;;eAAA;;;;;;8BAzNiC;6BAS1B;sCAEiB;wEACmB;6BACf;;;;;;;;;;AAG5B,MAAM,EAAE,IAAI,EAAE,GAAG,gBAAU;AAE3B;;;;;;;;;;;;;;CAcC,GACD,MAAM,eAAyB;;IAC7B;;GAEC,GACD,MAAM,CAAC,eAAe,iBAAiB,GAAG,IAAA,eAAQ,EAA4B;QAC5E,UAAU;QACV,WAAW;QACX,UAAU;QACV,QAAQ;IACV;IAEA,MAAM,CAAC,cAAc,gBAAgB,GAAG,IAAA,eAAQ,EAAC;IACjD,MAAM,CAAC,YAAY,cAAc,GAAG,IAAA,eAAQ,EAAgB;IAE5D,SAAS;IACT,IAAA,gBAAS,EAAC;QACR,MAAM,iBAAiB;YACrB,IAAI;gBACF,MAAM,QAAQ,MAAM,iBAAW,CAAC,oBAAoB;gBACpD,iBAAiB;gBACjB,cAAc;YAChB,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,aAAa;gBAC3B,cAAc;YAChB,SAAU;gBACR,gBAAgB;YAClB;QACF;QAEA;IACF,GAAG,EAAE;IAEL,qBACE,2BAAC,sBAAO;QACN,qBACE,2BAAC,WAAK;YAAC,OAAM;;8BACX,2BAAC,uBAAgB;oBAAC,OAAO;wBAAE,UAAU;wBAAI,OAAO;oBAAU;;;;;;8BAC1D,2BAAC;8BAAK;;;;;;;;;;;;QAGV,qBACE,2BAAC,YAAM;YACL,MAAK;YACL,oBAAM,2BAAC,qBAAc;;;;;YACrB,SAAS,IAAM,OAAO,QAAQ,CAAC,MAAM;YACrC,MAAK;sBACN;;;;;;QAIH,OAAO;YACL,cAAc;YACd,cAAc;QAChB;QACA,WAAW;YACT,cAAc;YACd,eAAe;QACjB;QACA,WAAW;YACT,SAAS;QACX;QACA,SAAS;QACT,SAAS;kBAER,2BACC,2BAAC,WAAK;YACJ,SAAQ;YACR,aAAa;YACb,MAAK;YACL,QAAQ;;;;;iCAGV,2BAAC,UAAI;YAAC,UAAU;sBACd,cAAA,2BAAC,SAAG;gBAAC,QAAQ;oBAAC;oBAAG;iBAAE;;kCAEjB,2BAAC,SAAG;wBAAC,IAAI;wBAAI,IAAI;wBAAG,IAAI;wBAAG,IAAI;wBAAG,IAAI;kCACpC,cAAA,2BAAC;4BAAI,OAAO;gCAAE,WAAW;gCAAU,SAAS;4BAAW;;8CACrD,2BAAC;oCACC,OAAO;wCACL,UAAU;wCACV,YAAY;wCACZ,OAAO;wCACP,YAAY;wCACZ,cAAc;oCAChB;8CAEC,cAAc,QAAQ;;;;;;8CAEzB,2BAAC;oCACC,OAAO;wCACL,UAAU;wCACV,OAAO;wCACP,YAAY;oCACd;8CACD;;;;;;;;;;;;;;;;;kCAOL,2BAAC,SAAG;wBAAC,IAAI;wBAAI,IAAI;wBAAG,IAAI;wBAAG,IAAI;wBAAG,IAAI;kCACpC,cAAA,2BAAC;4BAAI,OAAO;gCAAE,WAAW;gCAAU,SAAS;4BAAW;;8CACrD,2BAAC;oCACC,OAAO;wCACL,UAAU;wCACV,YAAY;wCACZ,OAAO;wCACP,YAAY;wCACZ,cAAc;oCAChB;8CAEC,cAAc,SAAS;;;;;;8CAE1B,2BAAC;oCACC,OAAO;wCACL,UAAU;wCACV,OAAO;wCACP,YAAY;oCACd;8CACD;;;;;;;;;;;;;;;;;kCAOL,2BAAC,SAAG;wBAAC,IAAI;wBAAI,IAAI;wBAAG,IAAI;wBAAG,IAAI;wBAAG,IAAI;kCACpC,cAAA,2BAAC;4BAAI,OAAO;gCAAE,WAAW;gCAAU,SAAS;4BAAW;;8CACrD,2BAAC;oCACC,OAAO;wCACL,UAAU;wCACV,YAAY;wCACZ,OAAO;wCACP,YAAY;wCACZ,cAAc;oCAChB;8CAEC,cAAc,QAAQ;;;;;;8CAEzB,2BAAC;oCACC,OAAO;wCACL,UAAU;wCACV,OAAO;wCACP,YAAY;oCACd;8CACD;;;;;;;;;;;;;;;;;kCAOL,2BAAC,SAAG;wBAAC,IAAI;wBAAI,IAAI;wBAAG,IAAI;wBAAG,IAAI;wBAAG,IAAI;kCACpC,cAAA,2BAAC;4BAAI,OAAO;gCAAE,WAAW;gCAAU,SAAS;4BAAW;;8CACrD,2BAAC;oCACC,OAAO;wCACL,UAAU;wCACV,YAAY;wCACZ,OAAO;wCACP,YAAY;wCACZ,cAAc;oCAChB;8CAEC,cAAc,MAAM;;;;;;8CAEvB,2BAAC;oCACC,OAAO;wCACL,UAAU;wCACV,OAAO;wCACP,YAAY;oCACd;8CACD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUjB;GAtLM;KAAA;IAwLN,WAAe;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;4BCiFf;;;eAAA;;;;;;;8BAnSO;6BAYA;sCACiB;wEACmB;6BACf;sFAEK;;;;;;;;;;AAEjC,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,GAAG,gBAAU;AAElC;;;;;;;;;;;;;;CAcC,GACD,MAAM,eAAyB;;IAC7B;;GAEC,GACD,MAAM,CAAC,UAAU,YAAY,GAAG,IAAA,eAAQ,EAA4B;QAClE,MAAM;QACN,UAAU;QACV,OAAO;QACP,WAAW;QACX,cAAc;QACd,eAAe;QACf,eAAe;QACf,WAAW;QACX,QAAQ;IACV;IAEA,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,IAAA,eAAQ,EAAC;IACvD,MAAM,CAAC,eAAe,iBAAiB,GAAG,IAAA,eAAQ,EAAgB;IAElE,YAAY;IACZ,MAAM,CAAC,sBAAsB,wBAAwB,GAAG,IAAA,eAAQ,EAAC;IAEjE,SAAS;IACT,IAAA,gBAAS,EAAC;QACR,MAAM,gBAAgB;YACpB,IAAI;gBACF,MAAM,aAAa,MAAM,iBAAW,CAAC,oBAAoB;gBACzD,YAAY;gBACZ,iBAAiB;YACnB,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,eAAe;gBAC7B,iBAAiB;YACnB,SAAU;gBACR,mBAAmB;YACrB;QACF;QAEA;IACF,GAAG,EAAE;IAEL,qBACE,2BAAC,sBAAO;QACN,OAAM;QACN,OAAO;YACL,cAAc;YACd,cAAc;QAChB;;YAWC,8BACC,2BAAC,WAAK;gBACJ,SAAQ;gBACR,aAAa;gBACb,MAAK;gBACL,QAAQ;;;;;qCAGV,2BAAC,UAAI;gBAAC,UAAU;0BAEd,cAAA,2BAAC;8BACC,cAAA,2BAAC,SAAG;wBAAC,QAAQ;4BAAC;4BAAI;yBAAE;wBAAE,OAAM;;0CAE1B,2BAAC,SAAG;gCAAC,IAAI;gCAAI,IAAI;gCAAG,IAAI;gCAAG,IAAI;gCAAG,IAAI;0CACpC,cAAA,2BAAC,UAAI;oCAAC,SAAQ;oCAAS,OAAM;oCAAS,OAAO;wCAAE,QAAQ;oCAAO;8CAC5D,cAAA,2BAAC,YAAM;wCACL,MAAM;wCACN,OAAM;wCACN,OAAO;4CACL,iBAAiB;4CACjB,UAAU;4CACV,YAAY;4CACZ,QAAQ;4CACR,WAAW;wCACb;kDAEC,SAAS,IAAI,GACZ,SAAS,IAAI,CAAC,MAAM,CAAC,GAAG,WAAW,mBAEnC,2BAAC,mBAAY;;;;;;;;;;;;;;;;;;;;0CAOrB,2BAAC,SAAG;gCAAC,IAAI;gCAAI,IAAI;gCAAI,IAAI;gCAAI,IAAI;gCAAI,IAAI;0CACvC,cAAA,2BAAC;oCAAI,OAAO;wCAAE,aAAa;oCAAO;;sDAEhC,2BAAC;4CAAI,OAAO;gDAAE,cAAc;gDAAO,SAAS;gDAAQ,gBAAgB;gDAAiB,YAAY;4CAAS;;8DACxG,2BAAC;oDACC,OAAO;oDACP,OAAO;wDACL,QAAQ;wDACR,UAAU;wDACV,YAAY;wDACZ,OAAO;wDACP,YAAY;oDACd;8DAEC,SAAS,IAAI,IAAI;;;;;;8DAIpB,2BAAC,YAAM;oDACL,MAAK;oDACL,oBAAM,2BAAC,sBAAe;;;;;oDACtB,SAAS,IAAM,wBAAwB;oDACvC,OAAO;wDACL,cAAc;wDACd,OAAO;wDACP,QAAQ;wDACR,SAAS;wDACT,QAAQ;wDACR,OAAO;wDACP,SAAS;wDACT,YAAY;wDACZ,gBAAgB;oDAClB;;;;;;;;;;;;sDAKJ,2BAAC;4CAAI,OAAO;gDAAE,cAAc;4CAAM;sDAChC,cAAA,2BAAC,WAAK;gDAAC,MAAM;gDAAI,IAAI;;oDAClB,SAAS,KAAK,kBACb,2BAAC,WAAK;wDAAC,MAAM;wDAAG,OAAM;;0EACpB,2BAAC,mBAAY;gEACX,OAAO;oEACL,UAAU;oEACV,OAAO;gEACT;;;;;;0EAEF,2BAAC;gEACC,OAAO;oEACL,OAAO;oEACP,UAAU;oEACV,YAAY;gEACd;0EAEC,SAAS,KAAK;;;;;;;;;;;;oDAIpB,SAAS,SAAS,kBACjB,2BAAC,WAAK;wDAAC,MAAM;wDAAG,OAAM;;0EACpB,2BAAC,oBAAa;gEACZ,OAAO;oEACL,UAAU;oEACV,OAAO;gEACT;;;;;;0EAEF,2BAAC;gEACC,OAAO;oEACL,OAAO;oEACP,UAAU;oEACV,YAAY;gEACd;0EAEC,SAAS,SAAS;;;;;;;;;;;;;;;;;;;;;;;sDAQ7B,2BAAC;sDACC,cAAA,2BAAC,WAAK;gDAAC,MAAM;gDAAI,IAAI;;oDAElB,SAAS,YAAY,kBACpB,2BAAC,WAAK;wDAAC,MAAM;wDAAG,OAAM;kEACpB,cAAA,2BAAC;4DACC,OAAO;gEACL,UAAU;gEACV,OAAO;gEACP,YAAY;4DACd;;gEACD;gEACS,SAAS,YAAY;;;;;;;;;;;;oDAMlC,SAAS,aAAa,kBACrB,2BAAC,WAAK;wDAAC,MAAM;wDAAG,OAAM;;0EACpB,2BAAC,0BAAmB;gEAClB,OAAO;oEACL,UAAU;oEACV,OAAO;gEACT;;;;;;0EAEF,2BAAC;gEACC,OAAO;oEACL,UAAU;oEACV,OAAO;oEACP,YAAY;gEACd;;oEACD;oEACO,SAAS,aAAa;;;;;;;;;;;;;oDAMjC,SAAS,aAAa,kBACrB,2BAAC,WAAK;wDAAC,MAAM;wDAAG,OAAM;;0EACpB,2BAAC,mBAAY;gEACX,OAAO;oEACL,UAAU;oEACV,OAAO;gEACT;;;;;;0EAEF,2BAAC;gEACC,OAAO;oEACL,UAAU;oEACV,OAAO;oEACP,YAAY;gEACd;;oEACD;oEACK,SAAS,aAAa;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAchD,2BAAC,6BAAoB;gBACnB,SAAS;gBACT,UAAU,IAAM,wBAAwB;gBACxC,UAAU;gBACV,WAAW;oBACT,mBAAmB;oBACnB,QAAQ,GAAG,CAAC;gBACd;;;;;;;;;;;;AAIR;GA7PM;KAAA;IA+PN,WAAe;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;4BCmkBf;;;eAAA;;;;;;8BAn2BO;4BAC2B;6BAW3B;sCAC0B;wEACU;iCACf;6BACA;mCAMrB;2CACkD;;;;;;;;;;AAEzD,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,gBAAU;AAKlC,UAAU;AACV,MAAM,SAAS,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAkEhB,CAAC;AAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;CA8BC,GACD,MAAM,eAAyB;;IAC7B;;;;;;;;GAQC,GACD,MAAM,CAAC,OAAO,SAAS,GAAG,IAAA,eAAQ,EAAuB,EAAE;IAC3D,MAAM,CAAC,SAAS,WAAW,GAAG,IAAA,eAAQ,EAAC;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,IAAA,eAAQ,EAAgB;IAClD,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,IAAA,eAAQ,EAAgB;IAEtE;;;;;;GAMC,GAED;;;;;;;GAOC,GACD,MAAM,EAAE,YAAY,EAAE,eAAe,EAAE,GAAG,IAAA,aAAQ,EAAC;IACnD,MAAM,cAAc,yBAAA,mCAAA,aAAc,WAAW;IAE7C;;;;;;;;;;;;GAYC,GACD,MAAM,qBAAqB,IAAA,qCAAyB;IACpD,MAAM,gBAAgB,IAAA,qCAAyB;IAC/C,MAAM,iBAAiB,IAAA,iCAAqB;IAE5C,gBAAgB;IAChB,8BAA8B;IAC9B,qCAAqC;IACrC,uBAAuB;IACvB,gCAAgC;IAChC,MAAM,qBAAqB,CAAC,CAC1B,CAAA,kBACA,sBACA,eACA,YAAY,EAAE,KAAK,sBACnB,iBACA,IAAA,uCAAmB,EAAC,eAAe,mBAAkB;IAGvD,wCAAwC;IACxC,MAAM,sBAAsB,qBAAqB,qBAAqB;IAEtE,OAAO;IACP,QAAQ,GAAG,CAAC,sBAAsB;QAChC,WAAW,EAAE,wBAAA,kCAAA,YAAa,EAAE;QAC5B;QACA;QACA;QACA;QACA;QACA,4BAA4B,iBAAiB,qBAAqB,IAAA,uCAAmB,EAAC,eAAe,sBAAsB;QAC3H,yBAAyB,CAAC,EAAC,yBAAA,mCAAA,aAAc,WAAW;IACtD;IAEA,WAAW;IACX,IAAA,gBAAS,EAAC;QACR,MAAM,aAAa;YACjB,IAAI;gBACF,WAAW;gBACX,SAAS;gBACT,MAAM,YAAY,MAAM,iBAAW,CAAC,qBAAqB;gBACzD,SAAS;YACX,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,aAAa;gBAC3B,SAAS;YACX,SAAU;gBACR,WAAW;YACb;QACF;QAEA,mBAAmB;QACnB,IAAI,yBAAA,mCAAA,aAAc,WAAW,EAC3B;IAEJ,GAAG;QAAC,yBAAA,mCAAA,aAAc,WAAW;KAAC;IAE9B,mBAAmB;IACnB,IAAA,gBAAS,EAAC;QACR,4CAA4C;QAC5C,IAAI,EAAC,yBAAA,mCAAA,aAAc,WAAW,GAAE;YAC9B,SAAS,EAAE;YACX,SAAS;YACT,WAAW;YACX,mBAAmB;QACrB;IACF,GAAG;QAAC,yBAAA,mCAAA,aAAc,WAAW;KAAC;IAE9B,aAAa;IACb,IAAA,gBAAS,EAAC;QACR,QAAQ,GAAG,CAAC,aAAa;YACvB,WAAW,EAAE,wBAAA,kCAAA,YAAa,EAAE;YAC5B;YACA;QACF;IACF,GAAG;QAAC,wBAAA,kCAAA,YAAa,EAAE;QAAE;QAAqB;KAAmB;IAE7D,2BAA2B;IAE3B;;;;;;;;;;;;;;;;;;;;;;;;;;;GA2BC,GACD,MAAM,mBAAmB,OAAO,QAAgB;QAC9C;;;;;KAKC,GACD,IAAI,EAAC,yBAAA,mCAAA,aAAc,WAAW,GAC5B;QAGF,IAAI;YACF;;;;;;;OAOC,GACD,mBAAmB;YAEnB;;;;;OAKC,GACD,IAAI,WAAW,qBAAqB;gBAClC,YAAO,CAAC,IAAI,CAAC;gBACb;YACF;YAEA;;;;;;;OAOC,GACD,MAAM,WAAW,MAAM,qBAAW,CAAC,UAAU,CAAC;gBAAE;YAAO;YAEvD;;;;;;;OAOC,GACD,IACE,SAAS,oBAAoB,IAC7B,SAAS,IAAI,IACb,SAAS,IAAI,CAAC,EAAE,KAAK,QACrB;gBACA;;;;;;;SAOC,GACD,IAAI,eACF,IAAA,uCAAmB,EAAC,eAAe;gBAGrC;;;;;;;;;;;SAWC,GACD,IACE,CAAA,yBAAA,mCAAA,aAAc,aAAa,MAC3B,yBAAA,mCAAA,aAAc,aAAa,KAC3B,iBAEA,eAAe;gBACf,QAAQ,GAAG,CAAC;oBACV,aAAa,aAAa;oBAC1B,aAAa,aAAa;iBAC3B,EACE,IAAI,CAAC,CAAC,CAAC,aAAa,YAAY;oBAC/B,oBAAoB;oBACpB,IAAI,eAAe,YAAY,EAAE,KAAK,QACpC,gBAAgB;wBACd,GAAG,YAAY;wBACf;wBACA;oBACF;gBAEJ,GACC,KAAK,CAAC,CAAC;oBACN,QAAQ,KAAK,CAAC,uBAAuB;gBACrC,qBAAqB;gBACvB;gBAGJ;;;;;SAKC,GACD,YAAO,CAAC,IAAI,CAAC;YACf;QASF,EAAE,OAAO,OAAY;QACnB;;;;;;;;;;;;OAYC,GACD,iBAAiB;QACnB,SAAU;YACR;;;;;OAKC,GACD,mBAAmB;QACrB;IACF;IAEA,qBACE;;0BAEE,2BAAC;gBAAM,yBAAyB;oBAAE,QAAQ;gBAAO;;;;;;0BAEjD,2BAAC,sBAAO;gBACN,OAAM;gBACN,OAAO;oBACL,cAAc;oBACd,cAAc;gBAChB;gBACA,WAAW;oBACT,cAAc;oBACd,eAAe;gBACjB;gBACA,WAAW;oBACT,SAAS;gBACX;0BAEC,sBACC,2BAAC,WAAK;oBACJ,SAAQ;oBACR,aAAa;oBACb,MAAK;oBACL,QAAQ;oBACR,OAAO;wBAAE,cAAc;oBAAG;;;;;yCAG5B,2BAAC,UAAI;oBAAC,UAAU;8BACb,EAAC,yBAAA,mCAAA,aAAc,WAAW,kBACzB,2BAAC;wBAAI,OAAO;4BAAE,WAAW;4BAAU,SAAS;wBAAY;kCACtD,cAAA,2BAAC;4BAAK,MAAK;sCAAY;;;;;;;;;;+BAEvB,MAAM,MAAM,KAAK,KAAK,CAAC,wBACzB,2BAAC;wBAAI,OAAO;4BAAE,WAAW;4BAAU,SAAS;wBAAY;kCACtD,cAAA,2BAAC;4BAAK,MAAK;sCAAY;;;;;;;;;;6CAGzB,2BAAC,sBAAO;wBACN,YAAY;wBACZ,YAAY,CAAC;gCAkRQ,aAiCA,cAiCA,cAiCA;iDApXnB,2BAAC,sBAAO;gCACN,WAAU;gCACV,OAAO;oCACH,YACE,wBAAwB,KAAK,EAAE,GAC3B,8CACA;oCACN,cAAc;oCACd,WACE,wBAAwB,KAAK,EAAE,GAC3B,uCACA;oCACN,OAAO;oCACP,YAAY,CAAC,UAAU,EAAE,KAAK,SAAS,GAAG,YAAY,UAAU,CAAC;oCACjE,YAAY;oCACZ,QACE,wBAAwB,KAAK,EAAE,GAC3B,sBACA;oCACN,SAAS;oCACT,UAAU;oCACV,UAAU;gCACZ;gCACA,SAAS;gCACT,cAAc,CAAC;oCACb,IAAI,wBAAwB,KAAK,EAAE,EAAE;wCACnC,EAAE,aAAa,CAAC,KAAK,CAAC,SAAS,GAAG;wCAClC,EAAE,aAAa,CAAC,KAAK,CAAC,SAAS,GAC7B;oCACJ;gCACF;gCACA,cAAc,CAAC;oCACb,IAAI,wBAAwB,KAAK,EAAE,EAAE;wCACnC,EAAE,aAAa,CAAC,KAAK,CAAC,SAAS,GAAG;wCAClC,EAAE,aAAa,CAAC,KAAK,CAAC,SAAS,GAC7B;oCACJ;gCACF;0CAGA,cAAA,2BAAC,SAAG;oCACF,QAAQ;wCAAC;wCAAG;qCAAE;oCACd,OAAM;oCACN,OAAO;wCAAE,OAAO;oCAAO;;sDAGvB,2BAAC,SAAG;4CAAC,IAAI;4CAAI,IAAI;4CAAI,IAAI;4CAAI,IAAI;4CAAI,IAAI;sDACvC,cAAA,2BAAC,UAAI;gDAAC,QAAQ;gDAAC,KAAK;gDAAG,WAAU;;kEAE/B,2BAAC,UAAI;wDAAC,OAAM;wDAAS,KAAK;wDAAG,MAAK;;0EAChC,2BAAC;gEACC,OAAO;oEACL,QAAQ;oEACR,SAAS;oEACT,cAAc;oEACd,YAAY;oEACZ,SAAS;oEACT,YAAY;oEACZ,KAAK;gEACP;gEACA,SAAS,IACP,iBAAiB,KAAK,EAAE,EAAE,KAAK,IAAI;gEAErC,cAAc,CAAC;oEACb,EAAE,aAAa,CAAC,KAAK,CAAC,UAAU,GAC9B;gEACJ;gEACA,cAAc,CAAC;oEACb,EAAE,aAAa,CAAC,KAAK,CAAC,UAAU,GAC9B;gEACJ;;kFAEA,2BAAC;wEACC,MAAM;wEACN,OAAO;4EACL,UAAU;4EACV,OACE,wBAAwB,KAAK,EAAE,GAC3B,YACA;4EACN,YAAY;wEACd;kFAEC,KAAK,IAAI;;;;;;kFAEZ,2BAAC,oBAAa;wEACZ,OAAO;4EACL,UAAU;4EACV,OACE,wBAAwB,KAAK,EAAE,GAC3B,YACA;4EACN,eAAe;4EACf,SAAS;4EACT,YAAY;wEACd;;;;;;;;;;;;4DAKH,wBAAwB,KAAK,EAAE,kBAC9B,2BAAC;gEACC,OAAO;oEACL,YAAY;oEACZ,OAAO;oEACP,SAAS;oEACT,cAAc;oEACd,UAAU;oEACV,YAAY;gEACd;0EACD;;;;;;4DAOF,oBAAoB,KAAK,EAAE,kBAC1B,2BAAC,UAAI;gEAAC,OAAM;gEAAS,KAAK;;kFACxB,2BAAC,UAAI;wEAAC,MAAK;;;;;;kFACX,2BAAC;wEAAK,OAAO;4EAAE,UAAU;4EAAI,OAAO;wEAAO;kFAAG;;;;;;;;;;;;;;;;;;kEAQpD,2BAAC,UAAI;wDAAC,OAAM;wDAAS,KAAK;wDAAI,MAAK;wDAAO,WAAU;;0EAClD,2BAAC,aAAO;gEACN,OAAO,CAAC,QAAQ,EAAE,IAAI,KAAK,KAAK,SAAS,EAAE,cAAc,CAAC,SAAS,CAAC;0EAEpE,cAAA,2BAAC,UAAI;oEAAC,OAAM;oEAAS,KAAK;;sFACxB,2BAAC,0BAAmB;4EAClB,OAAO;gFAAE,OAAO;gFAAW,UAAU;4EAAG;;;;;;sFAE1C,2BAAC;4EACC,OAAO;gFAAE,UAAU;gFAAI,OAAO;4EAAU;;gFACzC;gFACM,IAAI,KACP,KAAK,SAAS,EACd,kBAAkB,CAAC;;;;;;;;;;;;;;;;;;4DAM1B,KAAK,UAAU,kBACd,2BAAC,aAAO;gEACN,OAAO,CAAC,QAAQ,EAAE,IAAI,KAAK,KAAK,UAAU,EAAE,cAAc,CAAC,SAAS,CAAC;0EAErE,cAAA,2BAAC,UAAI;oEAAC,OAAM;oEAAS,KAAK;;sFACxB,2BAAC,mBAAY;4EACX,OAAO;gFAAE,OAAO;gFAAW,UAAU;4EAAG;;;;;;sFAE1C,2BAAC;4EACC,OAAO;gFAAE,UAAU;gFAAI,OAAO;4EAAU;;gFACzC;gFACM,IAAI,KACP,KAAK,UAAU,EACf,kBAAkB,CAAC;;;;;;;;;;;;;;;;;;0EAM7B,2BAAC,aAAO;gEACN,OAAO,CAAC,MAAM,EAAE,KAAK,WAAW,CAAC,CAAC,CAAC;0EAEnC,cAAA,2BAAC,UAAI;oEAAC,OAAM;oEAAS,KAAK;;sFACxB,2BAAC,mBAAY;4EACX,OAAO;gFAAE,OAAO;gFAAW,UAAU;4EAAG;;;;;;sFAE1C,2BAAC;4EACC,OAAO;gFAAE,UAAU;gFAAI,OAAO;4EAAU;;gFAEvC,KAAK,WAAW;gFAAC;;;;;;;;;;;;;;;;;;;;;;;;kEAO1B,2BAAC,UAAI;wDAAC,OAAM;wDAAS,KAAK;wDAAG,MAAK;wDAAO,WAAU;;0EAEjD,2BAAC;gEACC,OAAO;oEACL,YAAY,KAAK,SAAS,GACtB,YACA;oEACJ,OAAO;oEACP,SAAS;oEACT,cAAc;oEACd,UAAU;oEACV,YAAY;oEACZ,SAAS;oEACT,YAAY;oEACZ,KAAK;gEACP;0EAEC,KAAK,SAAS,iBACb;;sFACE,2BAAC,oBAAa;4EAAC,OAAO;gFAAE,UAAU;4EAAE;;;;;;wEAAK;;iGAI3C;;sFACE,2BAAC,mBAAY;4EAAC,OAAO;gFAAE,UAAU;4EAAE;;;;;;wEAAK;;;;;;;;0EAO9C,2BAAC;gEACC,OAAO;oEACL,YAAY,KAAK,QAAQ,GAAG,YAAY;oEACxC,OAAO;oEACP,SAAS;oEACT,cAAc;oEACd,UAAU;oEACV,YAAY;oEACZ,SAAS;oEACT,YAAY;oEACZ,KAAK;gEACP;0EAEC,KAAK,QAAQ,iBACZ;;sFACE,2BAAC,0BAAmB;4EAAC,OAAO;gFAAE,UAAU;4EAAE;;;;;;wEAAK;;iGAIjD;;sFACE,2BAAC,0BAAmB;4EAAC,OAAO;gFAAE,UAAU;4EAAE;;;;;;wEAAK;;;;;;;;;;;;;;;;;;;;;;;;;sDAU3D,2BAAC,SAAG;4CAAC,IAAI;4CAAI,IAAI;4CAAI,IAAI;4CAAI,IAAI;4CAAI,IAAI;sDACvC,cAAA,2BAAC,SAAG;gDACF,QAAQ;oDAAC;oDAAG;iDAAE;gDACd,SAAS;oDAAE,IAAI;oDAAS,IAAI;gDAAM;;kEAGlC,2BAAC,SAAG;wDAAC,IAAI;wDAAG,IAAI;wDAAG,IAAI;wDAAG,IAAI;wDAAG,IAAI;kEACnC,cAAA,2BAAC;4DACC,OAAO;gEACL,YAAY;gEACZ,QAAQ;gEACR,cAAc;gEACd,SAAS;gEACT,WAAW;gEACX,UAAU;4DACZ;sEAEA,cAAA,2BAAC,UAAI;gEAAC,QAAQ;gEAAC,OAAM;gEAAS,KAAK;;kFACjC,2BAAC,kBAAW;wEACV,OAAO;4EAAE,OAAO;4EAAW,UAAU;wEAAG;;;;;;kFAE1C,2BAAC;wEACC,MAAM;wEACN,OAAO;4EACL,UAAU;4EACV,OAAO;4EACP,YAAY;wEACd;kFAEC,EAAA,cAAA,KAAK,KAAK,cAAV,kCAAA,YAAY,QAAQ,KAAI;;;;;;kFAE3B,2BAAC;wEAAK,OAAO;4EAAE,UAAU;4EAAG,OAAO;wEAAO;kFAAG;;;;;;;;;;;;;;;;;;;;;;kEAQnD,2BAAC,SAAG;wDAAC,IAAI;wDAAG,IAAI;wDAAG,IAAI;wDAAG,IAAI;wDAAG,IAAI;kEACnC,cAAA,2BAAC;4DACC,OAAO;gEACL,YAAY;gEACZ,QAAQ;gEACR,cAAc;gEACd,SAAS;gEACT,WAAW;gEACX,UAAU;4DACZ;sEAEA,cAAA,2BAAC,UAAI;gEAAC,QAAQ;gEAAC,OAAM;gEAAS,KAAK;;kFACjC,2BAAC,mBAAY;wEACX,OAAO;4EAAE,OAAO;4EAAW,UAAU;wEAAG;;;;;;kFAE1C,2BAAC;wEACC,MAAM;wEACN,OAAO;4EACL,UAAU;4EACV,OAAO;4EACP,YAAY;wEACd;kFAEC,EAAA,eAAA,KAAK,KAAK,cAAV,mCAAA,aAAY,SAAS,KAAI;;;;;;kFAE5B,2BAAC;wEAAK,OAAO;4EAAE,UAAU;4EAAG,OAAO;wEAAO;kFAAG;;;;;;;;;;;;;;;;;;;;;;kEAQnD,2BAAC,SAAG;wDAAC,IAAI;wDAAG,IAAI;wDAAG,IAAI;wDAAG,IAAI;wDAAG,IAAI;kEACnC,cAAA,2BAAC;4DACC,OAAO;gEACL,YAAY;gEACZ,QAAQ;gEACR,cAAc;gEACd,SAAS;gEACT,WAAW;gEACX,UAAU;4DACZ;sEAEA,cAAA,2BAAC,UAAI;gEAAC,QAAQ;gEAAC,OAAM;gEAAS,KAAK;;kFACjC,2BAAC,gCAAyB;wEACxB,OAAO;4EAAE,OAAO;4EAAW,UAAU;wEAAG;;;;;;kFAE1C,2BAAC;wEACC,MAAM;wEACN,OAAO;4EACL,UAAU;4EACV,OAAO;4EACP,YAAY;wEACd;kFAEC,EAAA,eAAA,KAAK,KAAK,cAAV,mCAAA,aAAY,QAAQ,KAAI;;;;;;kFAE3B,2BAAC;wEAAK,OAAO;4EAAE,UAAU;4EAAG,OAAO;wEAAO;kFAAG;;;;;;;;;;;;;;;;;;;;;;kEAQnD,2BAAC,SAAG;wDAAC,IAAI;wDAAG,IAAI;wDAAG,IAAI;wDAAG,IAAI;wDAAG,IAAI;kEACnC,cAAA,2BAAC;4DACC,OAAO;gEACL,YAAY;gEACZ,QAAQ;gEACR,cAAc;gEACd,SAAS;gEACT,WAAW;gEACX,UAAU;4DACZ;sEAEA,cAAA,2BAAC,UAAI;gEAAC,QAAQ;gEAAC,OAAM;gEAAS,KAAK;;kFACjC,2BAAC,gCAAyB;wEACxB,OAAO;4EAAE,OAAO;4EAAW,UAAU;wEAAG;;;;;;kFAE1C,2BAAC;wEACC,MAAM;wEACN,OAAO;4EACL,UAAU;4EACV,OAAO;4EACP,YAAY;wEACd;kFAEC,EAAA,eAAA,KAAK,KAAK,cAAV,mCAAA,aAAY,OAAO,KAAI;;;;;;kFAE1B,2BAAC;wEAAK,OAAO;4EAAE,UAAU;4EAAG,OAAO;wEAAO;kFAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAoB/E;GA/tBM;;QA+BsC,aAAQ;;;KA/B9C;IAiuBN,WAAe;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;4BCnJf;;;eAAA;;;;;;8BAltBO;6BAmBA;sCACqC;wEACD;6BACf;;;;;;;;;;AAG5B,MAAM,EAAE,IAAI,EAAE,GAAG,gBAAU;AAC3B,MAAM,EAAE,OAAO,EAAE,GAAG,UAAI;AASxB,MAAM,iBAAgD;;IACpD,aAAa;IACb,MAAM,CAAC,eAAe,iBAAiB,GAAG,IAAA,eAAQ,EAAiB,EAAE;IACrE,MAAM,CAAC,WAAW,aAAa,GAAG,IAAA,eAAQ,EAAoB;QAC5D,mBAAmB;QACnB,qBAAqB;QACrB,kBAAkB;QAClB,YAAY;QACZ,gBAAgB;QAChB,sBAAsB;IACxB;IACA,MAAM,CAAC,SAAS,WAAW,GAAG,IAAA,eAAQ,EAAC;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,IAAA,eAAQ,EAAgB;IAElD,WAAW;IACX,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,IAAA,eAAQ,EAAC;IACzD,MAAM,CAAC,SAAS,GAAG,UAAI,CAAC,OAAO;IAC/B,MAAM,CAAC,eAAe,iBAAiB,GAAG,IAAA,eAAQ,EAAgB;IAElE,QAAQ;IACR,MAAM,CAAC,WAAW,aAAa,GAAG,IAAA,eAAQ,EACxC;IAEF,MAAM,CAAC,YAAY,cAAc,GAAG,IAAA,eAAQ,EAAC;IAE7C,WAAW;IACX,IAAA,gBAAS,EAAC;QACR,MAAM,gBAAgB;YACpB,IAAI;gBACF,WAAW;gBACX,SAAS;gBAET,QAAQ,GAAG,CAAC;gBAEZ,8BAA8B;gBAC9B,MAAM,eAAe,iBAAW,CAAC,YAAY,GAAG,KAAK,CAAC,CAAC;oBACrD,QAAQ,KAAK,CAAC,eAAe;oBAC7B,OAAO,EAAE;gBACX;gBAEA,MAAM,eAAe,iBAAW,CAAC,YAAY,GAAG,KAAK,CAAC,CAAC;oBACrD,QAAQ,KAAK,CAAC,eAAe;oBAC7B,OAAO;wBACL,mBAAmB;wBACnB,qBAAqB;wBACrB,kBAAkB;wBAClB,YAAY;wBACZ,gBAAgB;wBAChB,sBAAsB;oBACxB;gBACF;gBAEA,MAAM,CAAC,OAAO,MAAM,GAAG,MAAM,QAAQ,GAAG,CAAC;oBAAC;oBAAc;iBAAa;gBAErE,QAAQ,GAAG,CAAC,8BAA8B;gBAC1C,QAAQ,GAAG,CAAC,4BAA4B;gBAExC,iBAAiB;gBACjB,aAAa;YACf,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,oBAAoB;gBAClC,SAAS;YACX,SAAU;gBACR,WAAW;YACb;QACF;QAEA;IACF,GAAG,EAAE;IAEL,mBAAmB;IACnB,MAAM,wBAAwB,AAAC,CAAA,iBAAiB,EAAE,AAAD,EAAG,MAAM,CAAC,CAAC;QAC1D,SAAS;QACT,IAAI,cAAc,aAAa,KAAK,MAAM,KAAK,GAAG,OAAO;QACzD,IAAI,cAAc,eAAe,KAAK,MAAM,KAAK,GAAG,OAAO;QAE3D,WAAW;QACX,IACE,cACA,CAAC,KAAK,KAAK,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,KAEzD,OAAO;QAGT,OAAO;IACT;IAEA,WAAW;IACX,MAAM,yBAAyB,OAAO;QACpC,IAAI;YACF,MAAM,OAAO,cAAc,IAAI,CAAC,CAAC,IAAM,EAAE,EAAE,KAAK;YAChD,IAAI,CAAC,MACH;YAGF,MAAM,YAAY,KAAK,MAAM,KAAK,IAAI,IAAI;YAE1C,MAAM,iBAAW,CAAC,UAAU,CAAC,IAAI;gBAAE,QAAQ;YAAU;YAErD,SAAS;YACT,iBACE,cAAc,GAAG,CAAC,CAAC,OACjB,KAAK,EAAE,KAAK,KAAK;oBAAE,GAAG,IAAI;oBAAE,QAAQ;gBAAU,IAAI;YAItD,SAAS;YACT,IAAI;gBACF,MAAM,QAAQ,MAAM,iBAAW,CAAC,YAAY;gBAC5C,aAAa;YACf,EAAE,OAAO,YAAY;YACnB,kBAAkB;YACpB;QACF,EAAE,OAAO,OAAO;QACd,iBAAiB;QACnB;IACF;IAEA,MAAM,wBAAwB,OAAO;QACnC,IAAI;YACF,IAAI,eAAe;gBACjB,WAAW;gBACX,MAAM,cAAc,MAAM,iBAAW,CAAC,UAAU,CAAC,eAAe;oBAC9D,OAAO,OAAO,IAAI;oBAClB,UAAU,OAAO,QAAQ;gBAC3B;gBAEA,iBACE,cAAc,GAAG,CAAC,CAAC,OACjB,KAAK,EAAE,KAAK,gBAAgB,cAAc;YAGhD,OAAO;gBACL,UAAU;gBACV,MAAM,UAAU,MAAM,iBAAW,CAAC,UAAU,CAAC;oBAC3C,OAAO,OAAO,IAAI;oBAClB,UAAU,OAAO,QAAQ;gBAC3B;gBAEA,iBAAiB;oBAAC;uBAAY;iBAAc;YAC9C;YAEA,SAAS;YACT,IAAI;gBACF,MAAM,QAAQ,MAAM,iBAAW,CAAC,YAAY;gBAC5C,aAAa;YACf,EAAE,OAAO,YAAY;YACnB,kBAAkB;YACpB;YAEA,aAAa;YACb,oBAAoB;YACpB,iBAAiB;YACjB,SAAS,WAAW;QACtB,EAAE,OAAO,OAAO;QACd,iBAAiB;QACnB;IACF;IAEA,MAAM,mBAAmB,OAAO;QAC9B,IAAI;YACF,MAAM,iBAAW,CAAC,UAAU,CAAC;YAC7B,iBAAiB,cAAc,MAAM,CAAC,CAAC,OAAS,KAAK,EAAE,KAAK;YAE5D,SAAS;YACT,IAAI;gBACF,MAAM,QAAQ,MAAM,iBAAW,CAAC,YAAY;gBAC5C,aAAa;YACf,EAAE,OAAO,YAAY;YACnB,kBAAkB;YACpB;QACF,EAAE,OAAO,OAAO;QACd,iBAAiB;QACnB;IACF;IAEA,qBACE,2BAAC,sBAAO;QACN,OAAM;QACN,OAAO;YACL,cAAc;YACd,QAAQ;YACR,WAAW;QACb;QACA,WAAW;YACT,cAAc;YACd,eAAe;QACjB;QACA,WAAW;YACT,SAAS;QACX;;0BAGA,2BAAC;gBACC,OAAO;oBACL,cAAc;oBACd,SAAS;oBACT,YAAY;oBACZ,cAAc;oBACd,QAAQ;gBACV;0BAGA,cAAA,2BAAC,SAAG;oBAAC,QAAQ;wBAAC;wBAAI;qBAAG;oBAAE,OAAM;;sCAE3B,2BAAC,SAAG;4BAAC,IAAI;4BAAI,IAAI;4BAAI,IAAI;4BAAG,IAAI;4BAAG,IAAI;sCACrC,cAAA,2BAAC,UAAI;gCAAC,OAAM;gCAAS,KAAK;gCAAI,OAAO;oCAAE,OAAO;gCAAO;;kDACnD,2BAAC,WAAK,CAAC,MAAM;wCACX,aAAY;wCACZ,UAAU;wCACV,sBAAQ,2BAAC,qBAAc;;;;;wCACvB,OAAO;wCACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;wCAC7C,OAAO;4CAAE,MAAM;wCAAE;wCACjB,MAAK;;;;;;kDAGP,2BAAC,YAAM;wCACL,MAAK;wCACL,oBAAM,2BAAC,mBAAY;;;;;wCACnB,SAAS;4CACP,iBAAiB;4CACjB,SAAS,WAAW;4CACpB,oBAAoB;wCACtB;wCACA,OAAO;4CACL,YAAY;4CACZ,aAAa;4CACb,WAAW;4CACX,YAAY;4CACZ,UAAU;wCACZ;wCACA,MAAK;kDACN;;;;;;;;;;;;;;;;;sCAOL,2BAAC,SAAG;4BAAC,IAAI;4BAAI,IAAI;4BAAI,IAAI;4BAAI,IAAI;4BAAI,IAAI;sCACvC,cAAA,2BAAC,UAAI;gCAAC,OAAM;gCAAS,SAAQ;gCAAS,MAAK;gCAAO,KAAK;;kDAErD,2BAAC;wCACC,OAAO;4CACL,YAAY;4CACZ,QAAQ;4CACR,cAAc;4CACd,SAAS;4CACT,SAAS;4CACT,YAAY;4CACZ,KAAK;4CACL,UAAU;4CACV,WAAW;wCACb;kDAEA,cAAA,2BAAC,aAAO;4CACN,OAAO,CAAC,KAAK,EAAE,UAAU,oBAAoB,CAAC,GAAG,EAAE,UAAU,cAAc,CAAC,CAAC,EAAE,UAAU,UAAU,CAAC,CAAC,CAAC;sDAEtG,cAAA,2BAAC,UAAI;gDAAC,OAAM;gDAAS,KAAK;;kEACxB,2BAAC;wDACC,OAAO;4DAAE,UAAU;4DAAI,YAAY;4DAAK,OAAO;wDAAU;kEAC1D;;;;;;kEAGD,2BAAC,cAAQ;wDACP,SAAS,UAAU,oBAAoB;wDACvC,MAAK;wDACL,OAAO;4DAAE,OAAO;wDAAG;wDACnB,aAAY;wDACZ,UAAU;;;;;;kEAEZ,2BAAC;wDACC,OAAO;4DAAE,UAAU;4DAAI,YAAY;4DAAK,OAAO;wDAAU;;4DAExD,UAAU,oBAAoB;4DAAC;;;;;;;;;;;;;;;;;;;;;;;kDAOxC,2BAAC;wCACC,OAAO;4CACL,YAAY;4CACZ,QAAQ;4CACR,cAAc;4CACd,SAAS;4CACT,SAAS;4CACT,YAAY;4CACZ,KAAK;4CACL,UAAU;4CACV,WAAW;wCACb;kDAEA,cAAA,2BAAC,aAAO;4CAAC,OAAO,CAAC,QAAQ,EAAE,UAAU,iBAAiB,CAAC,CAAC,CAAC;sDACvD,cAAA,2BAAC,UAAI;gDAAC,OAAM;gDAAS,KAAK;;kEACxB,2BAAC;wDACC,OAAO;4DACL,OAAO;4DACP,QAAQ;4DACR,cAAc;4DACd,YAAY;wDACd;;;;;;kEAEF,2BAAC;wDACC,OAAO;4DACL,UAAU;4DACV,YAAY;4DACZ,OAAO;wDACT;kEACD;;;;;;kEAGD,2BAAC;wDACC,OAAO;4DACL,UAAU;4DACV,YAAY;4DACZ,OAAO;wDACT;kEAEC,UAAU,iBAAiB;;;;;;;;;;;;;;;;;;;;;;kDAOpC,2BAAC;wCACC,OAAO;4CACL,YAAY;4CACZ,QAAQ;4CACR,cAAc;4CACd,SAAS;4CACT,SAAS;4CACT,YAAY;4CACZ,KAAK;4CACL,UAAU;4CACV,WAAW;wCACb;kDAEA,cAAA,2BAAC,aAAO;4CAAC,OAAO,CAAC,QAAQ,EAAE,UAAU,mBAAmB,CAAC,CAAC,CAAC;sDACzD,cAAA,2BAAC,UAAI;gDAAC,OAAM;gDAAS,KAAK;;kEACxB,2BAAC;wDACC,OAAO;4DACL,OAAO;4DACP,QAAQ;4DACR,cAAc;4DACd,YAAY;wDACd;;;;;;kEAEF,2BAAC;wDACC,OAAO;4DACL,UAAU;4DACV,YAAY;4DACZ,OAAO;wDACT;kEACD;;;;;;kEAGD,2BAAC;wDACC,OAAO;4DACL,UAAU;4DACV,YAAY;4DACZ,OAAO;wDACT;kEAEC,UAAU,mBAAmB;;;;;;;;;;;;;;;;;;;;;;kDAOtC,2BAAC;wCACC,OAAO;4CACL,YAAY;4CACZ,QAAQ;4CACR,cAAc;4CACd,SAAS;4CACT,SAAS;4CACT,YAAY;4CACZ,KAAK;4CACL,UAAU;4CACV,WAAW;wCACb;kDAEA,cAAA,2BAAC,aAAO;4CAAC,OAAO,CAAC,QAAQ,EAAE,UAAU,gBAAgB,CAAC,CAAC,CAAC;sDACtD,cAAA,2BAAC,UAAI;gDAAC,OAAM;gDAAS,KAAK;;kEACxB,2BAAC;wDACC,OAAO;4DACL,OAAO;4DACP,QAAQ;4DACR,cAAc;4DACd,YAAY;wDACd;;;;;;kEAEF,2BAAC;wDACC,OAAO;4DACL,UAAU;4DACV,YAAY;4DACZ,OAAO;wDACT;kEACD;;;;;;kEAGD,2BAAC;wDACC,OAAO;4DACL,UAAU;4DACV,YAAY;4DACZ,OAAO;wDACT;kEAEC,UAAU,gBAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAW3C,2BAAC,UAAI;gBACH,WAAW;gBACX,UAAU,CAAC,MAAQ,aAAa;gBAChC,MAAK;gBACL,OAAO;oBAAE,cAAc;gBAAE;;kCAEzB,2BAAC;wBAAQ,KAAI;uBAAS;;;;;kCACtB,2BAAC;wBAAQ,KAAI;uBAAU;;;;;kCACvB,2BAAC;wBAAQ,KAAI;uBAAU;;;;;;;;;;;YAIxB,sBACC,2BAAC,WAAK;gBACJ,SAAQ;gBACR,aAAa;gBACb,MAAK;gBACL,QAAQ;gBACR,OAAO;oBAAE,cAAc;gBAAG;;;;;qCAG5B,2BAAC,UAAI;gBAAC,UAAU;;kCACd,2BAAC,sBAAO;wBACN,YAAY;wBACZ,YAAY,CAAC;4BACX,qBACE,2BAAC;gCACC,WAAU;gCACV,OAAO;oCACL,SAAS;oCACT,cAAc;oCACd,cAAc;oCACd,YAAY;oCACZ,SAAS,KAAK,MAAM,KAAK,IAAI,MAAM;oCACnC,YAAY,CAAC,UAAU,EACrB,KAAK,MAAM,KAAK,IACZ,YACA,KAAK,QAAQ,KAAK,IAChB,YACA,KAAK,QAAQ,KAAK,IAChB,YACA,UACT,CAAC;oCACF,WAAW;gCACb;0CAEA,cAAA,2BAAC,UAAI;oCAAC,OAAM;oCAAS,KAAK;oCAAI,OAAO;wCAAE,OAAO;oCAAO;;sDAEnD,2BAAC,UAAI;4CAAC,QAAQ;4CAAC,OAAM;;gDAClB,KAAK,MAAM,KAAK,kBACf,2BAAC,UAAI;oDACH,OAAM;oDACN,SAAQ;oDACR,OAAO;wDACL,OAAO;wDACP,QAAQ;wDACR,cAAc;wDACd,YAAY;oDACd;8DAEA,cAAA,2BAAC,oBAAa;wDACZ,OAAO;4DAAE,OAAO;4DAAQ,UAAU;wDAAG;;;;;;;;;;2EAIzC,2BAAC;oDACC,OAAO;wDACL,OAAO;wDACP,QAAQ;wDACR,cAAc;wDACd,QAAQ,CAAC,UAAU,EACjB,KAAK,QAAQ,KAAK,IACd,YACA,KAAK,QAAQ,KAAK,IAChB,YACA,UACP,CAAC;oDACJ;;;;;;8DAIJ,2BAAC;oDACC,OAAO;wDACL,OAAO;wDACP,QAAQ;wDACR,YAAY;wDACZ,WAAW;oDACb;;;;;;;;;;;;sDAKJ,2BAAC,UAAI;4CAAC,QAAQ;4CAAC,OAAO;gDAAE,MAAM;4CAAE;;8DAC9B,2BAAC;oDACC,OAAO;wDACL,UAAU;wDACV,YAAY,KAAK,QAAQ,KAAK,IAAI,MAAM;wDACxC,gBACE,KAAK,MAAM,KAAK,IAAI,iBAAiB;wDACvC,OAAO,KAAK,MAAM,KAAK,IAAI,YAAY;oDACzC;8DAEC,KAAK,KAAK;;;;;;8DAIb,2BAAC,WAAK;oDAAC,OAAM;oDAAS,MAAM;oDAAG,OAAO;wDAAE,WAAW;oDAAE;;sEACnD,2BAAC,uBAAgB;4DACf,OAAO;gEACL,UAAU;gEACV,OAAO;4DACT;;;;;;sEAEF,2BAAC;4DAAK,MAAK;4DAAY,OAAO;gEAAE,UAAU;4DAAG;;gEAAG;gEACzC;gEACJ,IAAI,KAAK,KAAK,SAAS,EAAE,kBAAkB,CAAC;;;;;;;;;;;;;;;;;;;sDAMnD,2BAAC,cAAQ;4CACP,SAAS;gDAAC;6CAAQ;4CAClB,MAAM;gDACJ,OAAO;oDACL;wDACE,KAAK;wDACL,OACE,KAAK,MAAM,KAAK,IAAI,UAAU;wDAChC,oBACE,2BAAC,oBAAa;4DACZ,OAAO;gEACL,OACE,KAAK,MAAM,KAAK,IAAI,YAAY;gEAClC,UAAU;4DACZ;;;;;;oDAGN;oDACA;wDACE,KAAK;wDACL,OAAO;wDACP,oBAAM,2BAAC,mBAAY;4DAAC,OAAO;gEAAE,OAAO;4DAAU;;;;;;oDAChD;oDACA;wDACE,KAAK;wDACL,OAAO;wDACP,oBACE,2BAAC,qBAAc;4DAAC,OAAO;gEAAE,OAAO;4DAAU;;;;;;wDAE5C,QAAQ;oDACV;iDACD;gDACD,SAAS,CAAC,EAAE,GAAG,EAAE;oDACf,IAAI,QAAQ,YACV,uBAAuB,KAAK,EAAE;yDACzB,IAAI,QAAQ,QAAQ;wDACzB,iBAAiB,KAAK,EAAE;wDACxB,SAAS,cAAc,CAAC;4DACtB,MAAM,KAAK,KAAK;4DAChB,UAAU,KAAK,QAAQ;wDACzB;wDACA,oBAAoB;oDACtB,OAAO,IAAI,QAAQ,UACjB,iBAAiB,KAAK,EAAE;gDAE5B;4CACF;sDAEA,cAAA,2BAAC,YAAM;gDACL,MAAK;gDACL,MAAK;gDACL,oBAAM,2BAAC,mBAAY;;;;;gDACnB,OAAO;oDAAE,OAAO;oDAAI,QAAQ;gDAAG;;;;;;;;;;;;;;;;;;;;;;wBAM3C;;;;;;kCAIF,2BAAC,wBAAS;wBACR,OAAO,gBAAgB,WAAW;wBAClC,MAAM;wBACN,cAAc,CAAC;4BACb,IAAI,CAAC,SAAS;gCACZ,oBAAoB;gCACpB,SAAS,WAAW;4BACtB;wBACF;wBACA,MAAM;wBACN,QAAO;wBACP,UAAU;wBACV,cAAa;wBACb,OAAO;wBACP,YAAY;4BACV,UAAU;4BACV,gBAAgB;4BAChB,cAAc;4BACd,UAAU;wBACZ;wBACA,WAAW;4BACT,cAAc;gCACZ,YAAY,gBAAgB,SAAS;gCACrC,WAAW;4BACb;4BACA,mBAAmB;gCACjB,OAAO;oCACL,YAAY;oCACZ,aAAa;oCACb,WAAW;gCACb;gCACA,MAAM,8BAAgB,2BAAC,mBAAY;;;;2DAAM,2BAAC,mBAAY;;;;;4BACxD;4BACA,kBAAkB;gCAChB,OAAO;oCACL,aAAa;gCACf;4BACF;4BACA,SAAS;gCACP,oBAAoB;gCACpB,SAAS,WAAW;4BACtB;wBACF;wBACA,UAAU;;0CAEV,2BAAC,UAAI,CAAC,IAAI;gCACR,MAAK;gCACL,OAAM;gCACN,OAAO;oCAAC;wCAAE,UAAU;wCAAM,SAAS;oCAAU;iCAAE;0CAE/C,cAAA,2BAAC,WAAK;oCACJ,aAAY;oCACZ,MAAK;oCACL,OAAO;wCAAE,cAAc;oCAAE;;;;;;;;;;;0CAI7B,2BAAC,UAAI,CAAC,IAAI;gCACR,MAAK;gCACL,OAAM;gCACN,cAAc;gCACd,OAAO;oCAAC;wCAAE,UAAU;wCAAM,SAAS;oCAAS;iCAAE;0CAE9C,cAAA,2BAAC,YAAM;oCACL,MAAK;oCACL,SAAS;wCACP;4CAAE,OAAO;4CAAG,OAAO;wCAAO;wCAC1B;4CAAE,OAAO;4CAAG,OAAO;wCAAO;wCAC1B;4CAAE,OAAO;4CAAG,OAAO;wCAAO;qCAC3B;oCACD,OAAO;wCAAE,cAAc;oCAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQzC;GA7qBM;;QAgBe,UAAI,CAAC;;;KAhBpB;IA+qBN,WAAe;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;4BC5Zf;;;eAAA;;;;;;8BA9T4D;6BAUrD;wEACoC;;;;;;;;;;AAG3C,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,GAAG,gBAAU;AASlC;;;;;;;;;;;;;;;CAeC,GACD,MAAM,uBAA4D,CAAC,EACjE,OAAO,EACP,QAAQ,EACR,SAAS,EACT,QAAQ,EACT;;IACC,MAAM,CAAC,aAAa,GAAG,UAAI,CAAC,OAAO;IACnC,MAAM,CAAC,SAAS,GAAG,UAAI,CAAC,OAAO;IAC/B,MAAM,CAAC,WAAW,aAAa,GAAG,IAAA,eAAQ,EAAC;IAE3C,uBAAuB;IACvB,IAAA,gBAAS,EAAC;QACR,IAAI,WAAW,UACb,aAAa,cAAc,CAAC;YAC1B,MAAM,SAAS,IAAI;YACnB,OAAO,SAAS,KAAK;YACrB,WAAW,SAAS,SAAS;YAC7B,UAAU,SAAS,QAAQ;QAC7B;IAEJ,GAAG;QAAC;QAAS;QAAU;KAAa;IAEpC,WAAW;IACX,MAAM,uBAAuB;QAC3B,IAAI;YACF,MAAM,SAAS,MAAM,aAAa,cAAc;YAChD,QAAQ,GAAG,CAAC,WAAW;YAEvB,qBAAqB;YACrB,+CAA+C;YAE/C,aAAO,CAAC,OAAO,CAAC;YAChB,sBAAA,wBAAA;YACA;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,aAAa;YAC3B,aAAO,CAAC,KAAK,CAAC;QAChB;IACF;IAEA,WAAW;IACX,MAAM,mBAAmB;QACvB,IAAI;YACF,MAAM,SAAS,MAAM,SAAS,cAAc;YAC5C,QAAQ,GAAG,CAAC,SAAS;YAErB,mBAAmB;YACnB,wCAAwC;YAExC,aAAO,CAAC,OAAO,CAAC;YAChB,SAAS,WAAW;YACpB,sBAAA,wBAAA;YACA;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,WAAW;YACzB,aAAO,CAAC,KAAK,CAAC;QAChB;IACF;IAEA,SAAS;IACT,MAAM,eAAe;QACnB,aAAa,WAAW;QACxB,SAAS,WAAW;QACpB,aAAa;QACb;IACF;IAEA,gBAAgB;IAChB,MAAM,WAAW;QACf,IAAI,cAAc,YAChB;aAEA;IAEJ;IAEA,qBACE,2BAAC,WAAK;QACJ,qBACE,2BAAC,WAAK;YAAC,OAAM;;8BACX,2BAAC,sBAAe;oBAAC,OAAO;wBAAE,UAAU;wBAAI,OAAO;oBAAU;;;;;;8BACzD,2BAAC;oBAAM,OAAO;oBAAG,OAAO;wBAAE,QAAQ;wBAAG,UAAU;oBAAG;8BAAG;;;;;;;;;;;;QAKzD,MAAM;QACN,MAAM;QACN,UAAU;QACV,QAAQ,cAAc,aAAa,SAAS;QAC5C,YAAW;QAEX,cAAc;kBAGd,cAAA,2BAAC,UAAI;YACH,WAAW;YACX,UAAU;YACV,OAAO;gBAAE,WAAW;YAAG;YACvB,OAAO;gBACL;oBACE,KAAK;oBACL,qBACE,2BAAC,WAAK;wBAAC,OAAM;;0CACX,2BAAC,mBAAY;;;;;0CACb,2BAAC;0CAAK;;;;;;;;;;;;oBAGV,wBACE,2BAAC;;0CACC,2BAAC;gCAAI,OAAO;oCAAE,cAAc;gCAAG;0CAC7B,cAAA,2BAAC;oCAAK,OAAO;wCAAE,OAAO;wCAAW,UAAU;oCAAG;8CAAG;;;;;;;;;;;0CAKnD,2BAAC,UAAI;gCACH,MAAM;gCACN,QAAO;gCACP,cAAc;gCACd,cAAa;;kDAGb,2BAAC,UAAI,CAAC,IAAI;wCACR,qBACE,2BAAC;4CAAK,OAAO;gDAAE,YAAY;gDAAK,UAAU;4CAAG;sDAAG;;;;;;wCAIlD,MAAK;wCACL,OAAO;4CACL;gDAAE,UAAU;gDAAM,SAAS;4CAAQ;4CACnC;gDAAE,KAAK;gDAAG,SAAS;4CAAW;4CAC9B;gDAAE,KAAK;gDAAI,SAAS;4CAAc;yCACnC;kDAED,cAAA,2BAAC,WAAK;4CACJ,aAAY;4CACZ,OAAO;gDACL,cAAc;gDACd,UAAU;4CACZ;;;;;;;;;;;kDAKJ,2BAAC,UAAI,CAAC,IAAI;wCACR,qBACE,2BAAC;4CAAK,OAAO;gDAAE,YAAY;gDAAK,UAAU;4CAAG;sDAAG;;;;;;wCAIlD,MAAK;wCACL,OAAO;4CACL;gDAAE,KAAK;gDAAI,SAAS;4CAAc;yCACnC;kDAED,cAAA,2BAAC,WAAK;4CACJ,aAAY;4CACZ,OAAO;gDACL,cAAc;gDACd,UAAU;4CACZ;;;;;;;;;;;kDAKJ,2BAAC,UAAI,CAAC,IAAI;wCACR,qBACE,2BAAC;4CAAK,OAAO;gDAAE,YAAY;gDAAK,UAAU;4CAAG;sDAAG;;;;;;wCAIlD,MAAK;wCACL,OAAO;4CACL;gDAAE,UAAU;gDAAM,SAAS;4CAAQ;4CACnC;gDAAE,MAAM;gDAAS,SAAS;4CAAa;yCACxC;kDAED,cAAA,2BAAC,WAAK;4CACJ,aAAY;4CACZ,OAAO;gDACL,cAAc;gDACd,UAAU;4CACZ;;;;;;;;;;;kDAKJ,2BAAC,UAAI,CAAC,IAAI;wCACR,qBACE,2BAAC;4CAAK,OAAO;gDAAE,YAAY;gDAAK,UAAU;4CAAG;sDAAG;;;;;;wCAIlD,MAAK;wCACL,OAAO;4CACL;gDAAE,SAAS;gDAAiB,SAAS;4CAAa;yCACnD;kDAED,cAAA,2BAAC,WAAK;4CACJ,aAAY;4CACZ,OAAO;gDACL,cAAc;gDACd,UAAU;4CACZ;;;;;;;;;;;;;;;;;;;;;;;gBAMZ;gBACA;oBACE,KAAK;oBACL,qBACE,2BAAC,WAAK;wBAAC,OAAM;;0CACX,2BAAC,mBAAY;;;;;0CACb,2BAAC;0CAAK;;;;;;;;;;;;oBAGV,wBACE,2BAAC;;0CACC,2BAAC;gCAAI,OAAO;oCAAE,cAAc;oCAAI,WAAW;gCAAS;0CAClD,cAAA,2BAAC;oCAAK,OAAO;wCAAE,OAAO;wCAAW,UAAU;wCAAI,YAAY;oCAAI;8CAAG;;;;;;;;;;;0CAKpE,2BAAC,UAAI;gCACH,MAAM;gCACN,QAAO;gCACP,cAAc;gCACd,cAAa;0CAGb,cAAA,2BAAC,UAAI,CAAC,IAAI;oCACR,qBACE,2BAAC;wCAAK,OAAO;4CAAE,YAAY;4CAAK,UAAU;wCAAG;kDAAG;;;;;;oCAIlD,MAAK;oCACL,OAAO;wCACL;4CAAE,UAAU;4CAAM,SAAS;wCAAU;wCACrC;4CAAE,KAAK;4CAAG,SAAS;wCAAa;wCAChC;4CAAE,KAAK;4CAAI,SAAS;wCAAgB;qCACrC;oCACD,OAAO;wCAAE,cAAc;oCAAE;8CAEzB,cAAA,2BAAC,WAAK;wCACJ,aAAY;wCACZ,MAAK;wCACL,OAAO;4CACL,cAAc;4CACd,UAAU;4CACV,SAAS;4CACT,QAAQ;4CACR,YAAY;wCACd;wCACA,SAAS,CAAC;4CACR,EAAE,MAAM,CAAC,KAAK,CAAC,WAAW,GAAG;4CAC7B,EAAE,MAAM,CAAC,KAAK,CAAC,SAAS,GAAG;wCAC7B;wCACA,QAAQ,CAAC;4CACP,EAAE,MAAM,CAAC,KAAK,CAAC,WAAW,GAAG;4CAC7B,EAAE,MAAM,CAAC,KAAK,CAAC,SAAS,GAAG;wCAC7B;;;;;;;;;;;;;;;;;;;;;;gBAMZ;aACD;;;;;;;;;;;AAIT;GArRM;;QAMmB,UAAI,CAAC;QACT,UAAI,CAAC;;;KAPpB;IAuRN,WAAe;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;4BC5Hf;;;eAAA;;;;;;;4BAlMyB;6BACM;sCACP;uEACN;6EACU;8EACH;gFACE;8EACF;8EACA;;;;;;;;;;AAEzB;;;;;;;;;;;;;;;;;;;;;;;;;;CA0BC,GACD,MAAM,qBAA+B;;IACnC;;;;;;GAMC,GACD,MAAM,EAAE,YAAY,EAAE,OAAO,EAAE,GAAG,IAAA,aAAQ,EAAC;IAI3C;;;;;GAKC,GACD,IAAI,SACF,qBACE,2BAAC;QACC,OAAO;YACL,WAAW;YACX,YAAY;YACZ,SAAS;YACT,gBAAgB;YAChB,YAAY;QACd;;0BAEA,2BAAC,UAAI;gBAAC,MAAK;;;;;;0BACX,2BAAC;gBAAI,OAAO;oBAAE,YAAY;gBAAG;0BAAG;;;;;;;;;;;;IAKtC;;;;;;;;;GASC,GAED,qBACE;;0BAEE,2BAAC;gBACC,OAAO;oBACL,WAAW;oBACX,YAAY;oBACZ,SAAS;gBACX;0BAWA,cAAA,2BAAC,sBAAO;oBACN,OAAO;wBACL,OAAO;wBACP,WAAW;wBACX,cAAc;wBACd,WAAW;oBACb;oBACA,WAAW;wBACT,SAAS;oBACX;8BAWA,cAAA,2BAAC,SAAG;wBAAC,QAAQ;4BAAC;4BAAI;yBAAG;wBAAE,OAAO;4BAAE,QAAQ;wBAAE;;0CAaxC,2BAAC,SAAG;gCACF,IAAI;gCACJ,IAAI;gCACJ,IAAI;gCACJ,IAAI;gCACJ,IAAI;gCACJ,KAAK;;kDAGL,2BAAC,qBAAY;;;;;kDAGb,2BAAC,qBAAY;;;;;kDAGb,2BAAC,qBAAY;;;;;;;;;;;0CAWf,2BAAC,SAAG;gCACF,IAAI;gCACJ,IAAI;gCACJ,IAAI;gCACJ,IAAI;gCACJ,IAAI;gCACJ,KAAK;0CAEL,cAAA,2BAAC,uBAAc;;;;;;;;;;;;;;;;;;;;;;;;;;0BAgBvB,2BAAC,oBAAe;;;;;;;AAOtB;GA3JM;;QAQ8B,aAAQ;;;KARtC;IA6JN,WAAe;;;;;;;;;;;;;;;;;;;;;;;;;;;AClMf;;CAEC;;;;4BAUY;;;eAAA;;;;;gCAFc;;;;;;;;;AAEpB,MAAM;IACX;;GAEC,GACD,aAAa,eAAwC;QACnD,MAAM,WAAW,MAAM,mBAAU,CAAC,GAAG,CAAiB;QACtD,OAAO,SAAS,IAAI;IACtB;IAEA;;GAEC,GACD,aAAa,WAAW,OAA0B,EAAyB;QACzE,MAAM,WAAW,MAAM,mBAAU,CAAC,IAAI,CAAe,UAAU;QAC/D,OAAO,SAAS,IAAI;IACtB;IAEA;;GAEC,GACD,aAAa,WACX,EAAU,EACV,OAA0B,EACH;QACvB,MAAM,WAAW,MAAM,mBAAU,CAAC,GAAG,CACnC,CAAC,OAAO,EAAE,GAAG,CAAC,EACd;QAEF,OAAO,SAAS,IAAI;IACtB;IAEA;;GAEC,GACD,aAAa,WAAW,EAAU,EAAiB;QACjD,MAAM,mBAAU,CAAC,MAAM,CAAC,CAAC,OAAO,EAAE,GAAG,CAAC;IACxC;IAEA;;GAEC,GACD,aAAa,eAA2C;QACtD,MAAM,WAAW,MAAM,mBAAU,CAAC,GAAG,CAAoB;QACzD,OAAO,SAAS,IAAI;IACtB;AACF;;;;;;;;;;;;;;;;;;;;;;;;;ACzDA;;;CAGC,GAED,eAAe;;;;;;;;;;;;IAmDF,6BAA6B;eAA7B;;IAaA,uBAAuB;eAAvB;;IAxDA,2BAA2B;eAA3B;;IAkCA,mBAAmB;eAAnB;;IAjBA,mBAAmB;eAAnB;;;;;;;;;;;;;AAxBb,MAAM,qBAAqB;AAOpB,MAAM,8BAA8B,CAAC;IAC1C,IAAI;QACF,MAAM,UAAU,aAAa,OAAO,CAAC,CAAC,EAAE,mBAAmB,CAAC,EAAE,OAAO,CAAC;QACtE,IAAI,SACF,OAAO,IAAI,IAAI,KAAK,KAAK,CAAC;IAE9B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,eAAe;IAC/B;IACA,OAAO,IAAI;AACb;AAOO,MAAM,sBAAsB,CAAC,QAAgB;IAClD,IAAI;QACF,MAAM,UAAU,4BAA4B;QAC5C,QAAQ,GAAG,CAAC;QACZ,aAAa,OAAO,CAAC,CAAC,EAAE,mBAAmB,CAAC,EAAE,OAAO,CAAC,EAAE,KAAK,SAAS,CAAC;eAAI;SAAQ;QACnF,QAAQ,GAAG,CAAC,CAAC,UAAU,EAAE,OAAO,KAAK,EAAE,OAAO,CAAC;IACjD,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,eAAe;IAC/B;AACF;AAQO,MAAM,sBAAsB,CAAC,QAAgB;IAClD,MAAM,UAAU,4BAA4B;IAC5C,OAAO,QAAQ,GAAG,CAAC;AACrB;AAMO,MAAM,gCAAgC,CAAC;IAC5C,IAAI;QACF,aAAa,UAAU,CAAC,CAAC,EAAE,mBAAmB,CAAC,EAAE,OAAO,CAAC;QACzD,QAAQ,GAAG,CAAC,CAAC,IAAI,EAAE,OAAO,OAAO,CAAC;IACpC,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,eAAe;IAC/B;AACF;AAMO,MAAM,0BAA0B;IACrC,MAAM,OAAiB,EAAE;IACzB,IAAK,IAAI,IAAI,GAAG,IAAI,aAAa,MAAM,EAAE,IAAK;QAC5C,MAAM,MAAM,aAAa,GAAG,CAAC;QAC7B,IAAI,OAAO,IAAI,UAAU,CAAC,qBACxB,KAAK,IAAI,CAAC;IAEd;IACA,OAAO;AACT"}