{"version": 3, "sources": ["umi.6951127563987657784.hot-update.js", "src/.umi/core/route.tsx"], "sourcesContent": ["globalThis.makoModuleHotUpdate(\r\n  'src/.umi/umi.ts?hmr',\r\n  {\r\n    modules: {},\r\n  },\r\n  function (runtime) {\r\n    runtime._h='5341233993249395350';\nruntime.updateEnsure2Map({\"src/.umi/core/EmptyRoute.tsx\":[\"src/.umi/core/EmptyRoute.tsx\"],\"src/.umi/plugin-layout/Layout.tsx\":[\"vendors\",\"src/.umi/plugin-layout/Layout.tsx\"],\"src/pages/404.tsx\":[\"p__404\"],\"src/pages/Dashboard/index.tsx\":[\"p__Dashboard__index\"],\"src/pages/help/index.tsx\":[\"p__help__index\"],\"src/pages/invite/[token].tsx\":[\"common\",\"p__invite__token\"],\"src/pages/personal-center/index.tsx\":[\"src/pages/personal-center/index.tsx\"],\"src/pages/settings/index.tsx\":[\"p__settings__index\"],\"src/pages/user/login/index.tsx\":[\"common\",\"p__user__login__index\"]});;\r\n  },\r\n);\r\n", "// @ts-nocheck\n// This file is generated by <PERSON><PERSON> automatically\n// DO NOT CHANGE IT MANUALLY!\nimport React from 'react';\n\nexport async function getRoutes() {\n  const routes = {\"1\":{\"path\":\"/user\",\"layout\":false,\"id\":\"1\"},\"2\":{\"name\":\"login\",\"path\":\"/user/login\",\"parentId\":\"1\",\"id\":\"2\"},\"3\":{\"path\":\"/invite/:token\",\"name\":\"团队邀请\",\"layout\":false,\"id\":\"3\"},\"4\":{\"path\":\"/dashboard\",\"name\":\"仪表盘\",\"icon\":\"dashboard\",\"parentId\":\"ant-design-pro-layout\",\"id\":\"4\"},\"5\":{\"path\":\"/personal-center\",\"name\":\"个人中心\",\"icon\":\"user\",\"layout\":false,\"id\":\"5\"},\"6\":{\"path\":\"/settings\",\"name\":\"设置\",\"icon\":\"setting\",\"layout\":false,\"id\":\"6\"},\"7\":{\"path\":\"/help\",\"name\":\"帮助中心\",\"icon\":\"question\",\"hideInMenu\":true,\"parentId\":\"ant-design-pro-layout\",\"id\":\"7\"},\"8\":{\"path\":\"/\",\"redirect\":\"/dashboard\",\"parentId\":\"ant-design-pro-layout\",\"id\":\"8\"},\"9\":{\"path\":\"*\",\"layout\":false,\"id\":\"9\"},\"ant-design-pro-layout\":{\"id\":\"ant-design-pro-layout\",\"path\":\"/\",\"isLayout\":true}} as const;\n  return {\n    routes,\n    routeComponents: {\n'1': React.lazy(() => import('./EmptyRoute')),\n'2': React.lazy(() => import(/* webpackChunkName: \"p__user__login__index\" */'@/pages/user/login/index.tsx')),\n'3': React.lazy(() => import(/* webpackChunkName: \"p__invite__token\" */'@/pages/invite/[token].tsx')),\n'4': React.lazy(() => import(/* webpackChunkName: \"p__Dashboard__index\" */'@/pages/Dashboard/index.tsx')),\n'5': React.lazy(() => import(/* webpackChunkName: \"p__personal-center__index\" */'@/pages/personal-center/index.tsx')),\n'6': React.lazy(() => import(/* webpackChunkName: \"p__settings__index\" */'@/pages/settings/index.tsx')),\n'7': React.lazy(() => import(/* webpackChunkName: \"p__help__index\" */'@/pages/help/index.tsx')),\n'8': React.lazy(() => import('./EmptyRoute')),\n'9': React.lazy(() => import(/* webpackChunkName: \"p__404\" */'@/pages/404.tsx')),\n'ant-design-pro-layout': React.lazy(() => import(/* webpackChunkName: \"umi__plugin-layout__Layout\" */'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/plugin-layout/Layout.tsx')),\n},\n  };\n}\n"], "names": [], "mappings": "AAAA,WAAW,mBAAmB,CAC5B,uBACA;IACE,SAAS;;;;;;;wCCES;;;2BAAA;;;;;;mFAFJ;;;;;;;;;YAEX,eAAe;gBACpB,MAAM,SAAS;oBAAC,KAAI;wBAAC,QAAO;wBAAQ,UAAS;wBAAM,MAAK;oBAAG;oBAAE,KAAI;wBAAC,QAAO;wBAAQ,QAAO;wBAAc,YAAW;wBAAI,MAAK;oBAAG;oBAAE,KAAI;wBAAC,QAAO;wBAAiB,QAAO;wBAAO,UAAS;wBAAM,MAAK;oBAAG;oBAAE,KAAI;wBAAC,QAAO;wBAAa,QAAO;wBAAM,QAAO;wBAAY,YAAW;wBAAwB,MAAK;oBAAG;oBAAE,KAAI;wBAAC,QAAO;wBAAmB,QAAO;wBAAO,QAAO;wBAAO,UAAS;wBAAM,MAAK;oBAAG;oBAAE,KAAI;wBAAC,QAAO;wBAAY,QAAO;wBAAK,QAAO;wBAAU,UAAS;wBAAM,MAAK;oBAAG;oBAAE,KAAI;wBAAC,QAAO;wBAAQ,QAAO;wBAAO,QAAO;wBAAW,cAAa;wBAAK,YAAW;wBAAwB,MAAK;oBAAG;oBAAE,KAAI;wBAAC,QAAO;wBAAI,YAAW;wBAAa,YAAW;wBAAwB,MAAK;oBAAG;oBAAE,KAAI;wBAAC,QAAO;wBAAI,UAAS;wBAAM,MAAK;oBAAG;oBAAE,yBAAwB;wBAAC,MAAK;wBAAwB,QAAO;wBAAI,YAAW;oBAAI;gBAAC;gBAC7wB,OAAO;oBACL;oBACA,iBAAiB;wBACrB,mBAAK,cAAK,CAAC,IAAI,CAAC,IAAM;wBACtB,mBAAK,cAAK,CAAC,IAAI,CAAC,IAAM;wBACtB,mBAAK,cAAK,CAAC,IAAI,CAAC,IAAM;wBACtB,mBAAK,cAAK,CAAC,IAAI,CAAC,IAAM;wBACtB,mBAAK,cAAK,CAAC,IAAI,CAAC,IAAM;wBACtB,mBAAK,cAAK,CAAC,IAAI,CAAC,IAAM;wBACtB,mBAAK,cAAK,CAAC,IAAI,CAAC,IAAM;wBACtB,mBAAK,cAAK,CAAC,IAAI,CAAC,IAAM;wBACtB,mBAAK,cAAK,CAAC,IAAI,CAAC,IAAM;wBACtB,uCAAyB,cAAK,CAAC,IAAI,CAAC,IAAM;oBAC1C;gBACE;YACF;;;;;;;;;;;;;;;;;;;;;;;IDnBc;AACZ,GACA,SAAU,OAAO;IACf,QAAQ,EAAE,GAAC;IACf,QAAQ,gBAAgB,CAAC;QAAC,gCAA+B;YAAC;SAA+B;QAAC,qCAAoC;YAAC;YAAU;SAAoC;QAAC,qBAAoB;YAAC;SAAS;QAAC,iCAAgC;YAAC;SAAsB;QAAC,4BAA2B;YAAC;SAAiB;QAAC,gCAA+B;YAAC;YAAS;SAAmB;QAAC,uCAAsC;YAAC;SAAsC;QAAC,gCAA+B;YAAC;SAAqB;QAAC,kCAAiC;YAAC;YAAS;SAAwB;IAAA;;AACrjB"}