{"version": 3, "sources": ["umi.5341233993249395350.hot-update.js", "src/.umi/plugin-layout/icons.tsx"], "sourcesContent": ["globalThis.makoModuleHotUpdate(\r\n  'src/.umi/umi.ts?hmr',\r\n  {\r\n    modules: {},\r\n  },\r\n  function (runtime) {\r\n    runtime._h='7889784115315191356';\nruntime.updateEnsure2Map({\"src/.umi/core/EmptyRoute.tsx\":[\"src/.umi/core/EmptyRoute.tsx\"],\"src/.umi/plugin-layout/Layout.tsx\":[\"vendors\",\"src/.umi/plugin-layout/Layout.tsx\"],\"src/pages/404.tsx\":[\"p__404\"],\"src/pages/Dashboard/index.tsx\":[\"p__Dashboard__index\"],\"src/pages/help/index.tsx\":[\"p__help__index\"],\"src/pages/invite/[token].tsx\":[\"common\",\"p__invite__token\"],\"src/pages/personal-center/index.tsx\":[\"src/pages/personal-center/index.tsx\"],\"src/pages/settings/index.tsx\":[\"p__settings__index\"],\"src/pages/user/login/index.tsx\":[\"common\",\"p__user__login__index\"]});;\r\n  },\r\n);\r\n", "// @ts-nocheck\n// This file is generated by <PERSON><PERSON> automatically\n// DO NOT CHANGE IT MANUALLY!\nimport DashboardOutlined from 'H:/projects/IdeaProjects/teamAuth/frontend/node_modules/@umijs/plugins/node_modules/@ant-design/icons/es/icons/DashboardOutlined';\nimport UserOutlined from 'H:/projects/IdeaProjects/teamAuth/frontend/node_modules/@umijs/plugins/node_modules/@ant-design/icons/es/icons/UserOutlined';\nimport SettingOutlined from 'H:/projects/IdeaProjects/teamAuth/frontend/node_modules/@umijs/plugins/node_modules/@ant-design/icons/es/icons/SettingOutlined';\nimport QuestionOutlined from 'H:/projects/IdeaProjects/teamAuth/frontend/node_modules/@umijs/plugins/node_modules/@ant-design/icons/es/icons/QuestionOutlined';\nexport default { DashboardOutlined, UserOutlined, SettingOutlined, QuestionOutlined };\n"], "names": [], "mappings": "AAAA,WAAW,mBAAmB,CAC5B,uBACA;IACE,SAAS;;;;;;wCCIb;;;2BAAA;;;;;;+FAJ8B;0FACL;6FACG;8FACC;;;;;;;;;gBAC7B,WAAe;gBAAE,mBAAA,0BAAiB;gBAAE,cAAA,qBAAY;gBAAE,iBAAA,wBAAe;gBAAE,kBAAA,yBAAgB;YAAC;;;;;;;;;;;;;;;;;;;;;;;IDJtE;AACZ,GACA,SAAU,OAAO;IACf,QAAQ,EAAE,GAAC;IACf,QAAQ,gBAAgB,CAAC;QAAC,gCAA+B;YAAC;SAA+B;QAAC,qCAAoC;YAAC;YAAU;SAAoC;QAAC,qBAAoB;YAAC;SAAS;QAAC,iCAAgC;YAAC;SAAsB;QAAC,4BAA2B;YAAC;SAAiB;QAAC,gCAA+B;YAAC;YAAS;SAAmB;QAAC,uCAAsC;YAAC;SAAsC;QAAC,gCAA+B;YAAC;SAAqB;QAAC,kCAAiC;YAAC;YAAS;SAAwB;IAAA;;AACrjB"}