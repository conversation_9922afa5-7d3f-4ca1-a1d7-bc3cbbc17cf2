{"version": 3, "sources": ["src_pages_personal-center_index_tsx-async.6780611008479686921.hot-update.js", "src/pages/personal-center/DataOverview.tsx"], "sourcesContent": ["globalThis.makoModuleHotUpdate(\r\n  'src/pages/personal-center/index.tsx',\r\n  {\r\n    modules: {},\r\n  },\r\n  function (runtime) {\r\n    runtime._h='15628441034446508192';\nruntime.updateEnsure2Map({\"src/.umi/core/EmptyRoute.tsx\":[\"src/.umi/core/EmptyRoute.tsx\"],\"src/.umi/plugin-layout/Layout.tsx\":[\"vendors\",\"src/.umi/plugin-layout/Layout.tsx\"],\"src/pages/404.tsx\":[\"p__404\"],\"src/pages/Dashboard/index.tsx\":[\"p__Dashboard__index\"],\"src/pages/help/index.tsx\":[\"p__help__index\"],\"src/pages/invite/[token].tsx\":[\"common\",\"p__invite__token\"],\"src/pages/personal-center/index.tsx\":[\"src/pages/personal-center/index.tsx\"],\"src/pages/settings/index.tsx\":[\"p__settings__index\"],\"src/pages/team-management/index.tsx\":[\"src/pages/team-management/index.tsx\"],\"src/pages/user/login/index.tsx\":[\"common\",\"p__user__login__index\"],\"src/services/invitation.ts\":[\"src/services/invitation.ts\"]});;\r\n  },\r\n);\r\n", "import { BarChartOutlined } from '@ant-design/icons';\nimport {\n  Alert,\n  Button,\n  Col,\n  Row,\n  Space,\n  Spin,\n  Typography,\n} from 'antd';\nimport { ReloadOutlined } from '@ant-design/icons';\nimport { ProCard } from '@ant-design/pro-components';\nimport React, { useEffect, useState } from 'react';\nimport { UserService } from '@/services/user';\nimport type { UserPersonalStatsResponse } from '@/types/user';\n\nconst { Text } = Typography;\n\n/**\n * 数据概览组件\n *\n * 显示用户的个人统计数据，包括车辆、人员、预警、告警等指标。\n * 这是从原UserProfileCard组件中提取的数据概览部分。\n *\n * 主要功能：\n * 1. 显示车辆数量统计\n * 2. 显示人员数量统计\n * 3. 显示预警数量统计\n * 4. 显示告警数量统计\n *\n * 数据来源：\n * - 个人统计数据：通过UserService.getUserPersonalStats()获取\n */\nconst DataOverview: React.FC = () => {\n  /**\n   * 个人统计数据状态管理\n   */\n  const [personalStats, setPersonalStats] = useState<UserPersonalStatsResponse>({\n    vehicles: 0,\n    personnel: 0,\n    warnings: 0,\n    alerts: 0,\n  });\n\n  const [statsLoading, setStatsLoading] = useState(true);\n  const [statsError, setStatsError] = useState<string | null>(null);\n\n  // 获取统计数据\n  useEffect(() => {\n    const fetchStatsData = async () => {\n      try {\n        const stats = await UserService.getUserPersonalStats();\n        setPersonalStats(stats);\n        setStatsError(null);\n      } catch (error) {\n        console.error('获取统计数据失败:', error);\n        setStatsError('获取统计数据失败，请稍后重试');\n      } finally {\n        setStatsLoading(false);\n      }\n    };\n\n    fetchStatsData();\n  }, []);\n\n  return (\n    <ProCard\n      title={\n        <Space align=\"center\">\n          <BarChartOutlined style={{ fontSize: 16, color: '#1890ff' }} />\n          <span>数据概览</span>\n        </Space>\n      }\n      style={{\n        marginBottom: 16,\n        borderRadius: 8,\n      }}\n      headStyle={{\n        borderBottom: '1px solid #f0f0f0',\n        paddingBottom: 12,\n      }}\n      bodyStyle={{\n        padding: '16px',\n      }}\n      hoverable\n      loading={statsLoading}\n    >\n      {statsError ? (\n        <Alert\n          message=\"数据概览加载失败\"\n          description={statsError}\n          type=\"error\"\n          showIcon\n        />\n      ) : (\n        <Spin spinning={statsLoading}>\n          <Row gutter={[8, 8]}>\n            {/* 车辆统计 */}\n            <Col xs={12} sm={6} md={6} lg={6} xl={6}>\n              <div style={{ textAlign: 'center', padding: '16px 8px' }}>\n                <div\n                  style={{\n                    fontSize: 28,\n                    fontWeight: 700,\n                    color: '#1890ff',\n                    lineHeight: 1,\n                    marginBottom: 6,\n                  }}\n                >\n                  {personalStats.vehicles}\n                </div>\n                <div\n                  style={{\n                    fontSize: 13,\n                    color: '#8c8c8c',\n                    fontWeight: 500,\n                  }}\n                >\n                  车辆\n                </div>\n              </div>\n            </Col>\n\n            {/* 人员统计 */}\n            <Col xs={12} sm={6} md={6} lg={6} xl={6}>\n              <div style={{ textAlign: 'center', padding: '16px 8px' }}>\n                <div\n                  style={{\n                    fontSize: 28,\n                    fontWeight: 700,\n                    color: '#52c41a',\n                    lineHeight: 1,\n                    marginBottom: 6,\n                  }}\n                >\n                  {personalStats.personnel}\n                </div>\n                <div\n                  style={{\n                    fontSize: 13,\n                    color: '#8c8c8c',\n                    fontWeight: 500,\n                  }}\n                >\n                  人员\n                </div>\n              </div>\n            </Col>\n\n            {/* 预警统计 */}\n            <Col xs={12} sm={6} md={6} lg={6} xl={6}>\n              <div style={{ textAlign: 'center', padding: '16px 8px' }}>\n                <div\n                  style={{\n                    fontSize: 28,\n                    fontWeight: 700,\n                    color: '#faad14',\n                    lineHeight: 1,\n                    marginBottom: 6,\n                  }}\n                >\n                  {personalStats.warnings}\n                </div>\n                <div\n                  style={{\n                    fontSize: 13,\n                    color: '#8c8c8c',\n                    fontWeight: 500,\n                  }}\n                >\n                  预警\n                </div>\n              </div>\n            </Col>\n\n            {/* 告警统计 */}\n            <Col xs={12} sm={6} md={6} lg={6} xl={6}>\n              <div style={{ textAlign: 'center', padding: '16px 8px' }}>\n                <div\n                  style={{\n                    fontSize: 28,\n                    fontWeight: 700,\n                    color: '#ff4d4f',\n                    lineHeight: 1,\n                    marginBottom: 6,\n                  }}\n                >\n                  {personalStats.alerts}\n                </div>\n                <div\n                  style={{\n                    fontSize: 13,\n                    color: '#8c8c8c',\n                    fontWeight: 500,\n                  }}\n                >\n                  告警\n                </div>\n              </div>\n            </Col>\n          </Row>\n        </Spin>\n      )}\n    </ProCard>\n  );\n};\n\nexport default DataOverview;\n"], "names": [], "mappings": "AAAA,WAAW,mBAAmB,CAC5B,uCACA;IACE,SAAS;;;;;;wCC4Mb;;;2BAAA;;;;;;0CA/MiC;yCAS1B;kDAEiB;oFACmB;yCACf;;;;;;;;;;YAG5B,MAAM,EAAE,IAAI,EAAE,GAAG,gBAAU;YAE3B;;;;;;;;;;;;;;CAcC,GACD,MAAM,eAAyB;;gBAC7B;;GAEC,GACD,MAAM,CAAC,eAAe,iBAAiB,GAAG,IAAA,eAAQ,EAA4B;oBAC5E,UAAU;oBACV,WAAW;oBACX,UAAU;oBACV,QAAQ;gBACV;gBAEA,MAAM,CAAC,cAAc,gBAAgB,GAAG,IAAA,eAAQ,EAAC;gBACjD,MAAM,CAAC,YAAY,cAAc,GAAG,IAAA,eAAQ,EAAgB;gBAE5D,SAAS;gBACT,IAAA,gBAAS,EAAC;oBACR,MAAM,iBAAiB;wBACrB,IAAI;4BACF,MAAM,QAAQ,MAAM,iBAAW,CAAC,oBAAoB;4BACpD,iBAAiB;4BACjB,cAAc;wBAChB,EAAE,OAAO,OAAO;4BACd,QAAQ,KAAK,CAAC,aAAa;4BAC3B,cAAc;wBAChB,SAAU;4BACR,gBAAgB;wBAClB;oBACF;oBAEA;gBACF,GAAG,EAAE;gBAEL,qBACE,2BAAC,sBAAO;oBACN,qBACE,2BAAC,WAAK;wBAAC,OAAM;;0CACX,2BAAC,uBAAgB;gCAAC,OAAO;oCAAE,UAAU;oCAAI,OAAO;gCAAU;;;;;;0CAC1D,2BAAC;0CAAK;;;;;;;;;;;;oBAGV,OAAO;wBACL,cAAc;wBACd,cAAc;oBAChB;oBACA,WAAW;wBACT,cAAc;wBACd,eAAe;oBACjB;oBACA,WAAW;wBACT,SAAS;oBACX;oBACA,SAAS;oBACT,SAAS;8BAER,2BACC,2BAAC,WAAK;wBACJ,SAAQ;wBACR,aAAa;wBACb,MAAK;wBACL,QAAQ;;;;;6CAGV,2BAAC,UAAI;wBAAC,UAAU;kCACd,cAAA,2BAAC,SAAG;4BAAC,QAAQ;gCAAC;gCAAG;6BAAE;;8CAEjB,2BAAC,SAAG;oCAAC,IAAI;oCAAI,IAAI;oCAAG,IAAI;oCAAG,IAAI;oCAAG,IAAI;8CACpC,cAAA,2BAAC;wCAAI,OAAO;4CAAE,WAAW;4CAAU,SAAS;wCAAW;;0DACrD,2BAAC;gDACC,OAAO;oDACL,UAAU;oDACV,YAAY;oDACZ,OAAO;oDACP,YAAY;oDACZ,cAAc;gDAChB;0DAEC,cAAc,QAAQ;;;;;;0DAEzB,2BAAC;gDACC,OAAO;oDACL,UAAU;oDACV,OAAO;oDACP,YAAY;gDACd;0DACD;;;;;;;;;;;;;;;;;8CAOL,2BAAC,SAAG;oCAAC,IAAI;oCAAI,IAAI;oCAAG,IAAI;oCAAG,IAAI;oCAAG,IAAI;8CACpC,cAAA,2BAAC;wCAAI,OAAO;4CAAE,WAAW;4CAAU,SAAS;wCAAW;;0DACrD,2BAAC;gDACC,OAAO;oDACL,UAAU;oDACV,YAAY;oDACZ,OAAO;oDACP,YAAY;oDACZ,cAAc;gDAChB;0DAEC,cAAc,SAAS;;;;;;0DAE1B,2BAAC;gDACC,OAAO;oDACL,UAAU;oDACV,OAAO;oDACP,YAAY;gDACd;0DACD;;;;;;;;;;;;;;;;;8CAOL,2BAAC,SAAG;oCAAC,IAAI;oCAAI,IAAI;oCAAG,IAAI;oCAAG,IAAI;oCAAG,IAAI;8CACpC,cAAA,2BAAC;wCAAI,OAAO;4CAAE,WAAW;4CAAU,SAAS;wCAAW;;0DACrD,2BAAC;gDACC,OAAO;oDACL,UAAU;oDACV,YAAY;oDACZ,OAAO;oDACP,YAAY;oDACZ,cAAc;gDAChB;0DAEC,cAAc,QAAQ;;;;;;0DAEzB,2BAAC;gDACC,OAAO;oDACL,UAAU;oDACV,OAAO;oDACP,YAAY;gDACd;0DACD;;;;;;;;;;;;;;;;;8CAOL,2BAAC,SAAG;oCAAC,IAAI;oCAAI,IAAI;oCAAG,IAAI;oCAAG,IAAI;oCAAG,IAAI;8CACpC,cAAA,2BAAC;wCAAI,OAAO;4CAAE,WAAW;4CAAU,SAAS;wCAAW;;0DACrD,2BAAC;gDACC,OAAO;oDACL,UAAU;oDACV,YAAY;oDACZ,OAAO;oDACP,YAAY;oDACZ,cAAc;gDAChB;0DAEC,cAAc,MAAM;;;;;;0DAEvB,2BAAC;gDACC,OAAO;oDACL,UAAU;oDACV,OAAO;oDACP,YAAY;gDACd;0DACD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAUjB;eA5KM;iBAAA;gBA8KN,WAAe;;;;;;;;;;;;;;;;;;;;;;;;;ID5MD;AACZ,GACA,SAAU,OAAO;IACf,QAAQ,EAAE,GAAC;IACf,QAAQ,gBAAgB,CAAC;QAAC,gCAA+B;YAAC;SAA+B;QAAC,qCAAoC;YAAC;YAAU;SAAoC;QAAC,qBAAoB;YAAC;SAAS;QAAC,iCAAgC;YAAC;SAAsB;QAAC,4BAA2B;YAAC;SAAiB;QAAC,gCAA+B;YAAC;YAAS;SAAmB;QAAC,uCAAsC;YAAC;SAAsC;QAAC,gCAA+B;YAAC;SAAqB;QAAC,uCAAsC;YAAC;SAAsC;QAAC,kCAAiC;YAAC;YAAS;SAAwB;QAAC,8BAA6B;YAAC;SAA6B;IAAA;;AAC/rB"}